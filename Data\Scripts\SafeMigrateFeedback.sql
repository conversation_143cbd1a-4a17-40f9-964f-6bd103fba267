-- Script seguro de migração para feedback diário
-- Execute primeiro o script CheckAndMigrateFeedback.sql para verificar a estrutura

-- ========================================
-- PASSO 1: BACKUP DE SEGURANÇA
-- ========================================
PRINT '=== CRIANDO BACKUP ===';

-- Verificar se backup já existe
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Feedbacks_Backup')
BEGIN
    PRINT 'Backup já existe. Criando novo backup com timestamp...';
    DECLARE @BackupName VARCHAR(50) = 'Feedbacks_Backup_' + FORMAT(GETDATE(), 'yyyyMMdd_HHmmss');
    DECLARE @SQL NVARCHAR(MAX) = 'SELECT * INTO ' + @BackupName + ' FROM Feedbacks';
    EXEC sp_executesql @SQL;
    PRINT 'Backup criado: ' + @BackupName;
END
ELSE
BEGIN
    SELECT * INTO Feedbacks_Backup FROM Feedbacks;
    PRINT 'Backup criado: Feedbacks_Backup';
END

-- ========================================
-- PASSO 2: ADICIONAR COLUNA FeedbackDate (SE NECESSÁRIO)
-- ========================================
PRINT '';
PRINT '=== ADICIONANDO COLUNA FeedbackDate ===';

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'FeedbackDate')
BEGIN
    ALTER TABLE Feedbacks ADD FeedbackDate DATE NULL;
    PRINT '✓ Coluna FeedbackDate adicionada';
END
ELSE
BEGIN
    PRINT '✓ Coluna FeedbackDate já existe';
END

-- ========================================
-- PASSO 3: MIGRAR DADOS (SE NECESSÁRIO)
-- ========================================
PRINT '';
PRINT '=== MIGRANDO DADOS ===';

-- Verificar se há dados para migrar
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'WeekNumber')
AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'Year')
AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'FeedbackDate')
BEGIN
    -- Verificar quantos registros precisam ser migrados
    DECLARE @RecordsToMigrate INT;
    SELECT @RecordsToMigrate = COUNT(*) FROM Feedbacks WHERE FeedbackDate IS NULL;
    
    IF @RecordsToMigrate > 0
    BEGIN
        PRINT 'Migrando ' + CAST(@RecordsToMigrate AS VARCHAR(10)) + ' registros...';
        
        -- Migrar dados: converter WeekNumber/Year para FeedbackDate
        -- Usando uma fórmula mais simples: primeiro dia do ano + (semana-1) * 7 dias
        UPDATE Feedbacks 
        SET FeedbackDate = DATEADD(DAY, (WeekNumber - 1) * 7, DATEFROMPARTS(Year, 1, 1))
        WHERE FeedbackDate IS NULL;
        
        PRINT '✓ Dados migrados com sucesso';
    END
    ELSE
    BEGIN
        PRINT '✓ Todos os dados já foram migrados';
    END
END
ELSE
BEGIN
    PRINT '! Não foi possível migrar dados (colunas antigas não encontradas)';
END

-- ========================================
-- PASSO 4: TORNAR FeedbackDate OBRIGATÓRIO
-- ========================================
PRINT '';
PRINT '=== CONFIGURANDO FeedbackDate COMO OBRIGATÓRIO ===';

-- Verificar se todos os registros têm FeedbackDate preenchido
DECLARE @NullRecords INT;
SELECT @NullRecords = COUNT(*) FROM Feedbacks WHERE FeedbackDate IS NULL;

IF @NullRecords = 0
BEGIN
    -- Verificar se a coluna já é NOT NULL
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Feedbacks' 
               AND COLUMN_NAME = 'FeedbackDate' 
               AND IS_NULLABLE = 'YES')
    BEGIN
        ALTER TABLE Feedbacks ALTER COLUMN FeedbackDate DATE NOT NULL;
        PRINT '✓ FeedbackDate definido como NOT NULL';
    END
    ELSE
    BEGIN
        PRINT '✓ FeedbackDate já é NOT NULL';
    END
END
ELSE
BEGIN
    PRINT '! Não é possível tornar FeedbackDate obrigatório: ' + CAST(@NullRecords AS VARCHAR(10)) + ' registros com valor NULL';
END

-- ========================================
-- PASSO 5: RENOMEAR COLUNA (SE NECESSÁRIO)
-- ========================================
PRINT '';
PRINT '=== RENOMEANDO COLUNA HighlightOfWeek ===';

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'HighlightOfWeek')
AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'HighlightOfDay')
BEGIN
    EXEC sp_rename 'Feedbacks.HighlightOfWeek', 'HighlightOfDay', 'COLUMN';
    PRINT '✓ Coluna renomeada: HighlightOfWeek → HighlightOfDay';
END
ELSE IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'HighlightOfDay')
BEGIN
    PRINT '✓ Coluna HighlightOfDay já existe';
END
ELSE
BEGIN
    PRINT '! Coluna HighlightOfWeek não encontrada';
END

-- ========================================
-- PASSO 6: CRIAR CONSTRAINT ÚNICA
-- ========================================
PRINT '';
PRINT '=== CRIANDO CONSTRAINT ÚNICA ===';

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'UQ_Feedbacks_UserDate')
BEGIN
    BEGIN TRY
        ALTER TABLE Feedbacks ADD CONSTRAINT UQ_Feedbacks_UserDate UNIQUE (UserId, FeedbackDate);
        PRINT '✓ Constraint única criada: UQ_Feedbacks_UserDate';
    END TRY
    BEGIN CATCH
        PRINT '! Erro ao criar constraint: ' + ERROR_MESSAGE();
        PRINT '  Pode haver dados duplicados. Verifique manualmente.';
    END CATCH
END
ELSE
BEGIN
    PRINT '✓ Constraint única já existe';
END

-- ========================================
-- PASSO 7: CRIAR ÍNDICES
-- ========================================
PRINT '';
PRINT '=== CRIANDO ÍNDICES ===';

-- Índice por data
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Feedbacks_FeedbackDate')
BEGIN
    CREATE INDEX IX_Feedbacks_FeedbackDate ON Feedbacks(FeedbackDate);
    PRINT '✓ Índice criado: IX_Feedbacks_FeedbackDate';
END
ELSE
BEGIN
    PRINT '✓ Índice IX_Feedbacks_FeedbackDate já existe';
END

-- Índice composto
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Feedbacks_UserId_FeedbackDate')
BEGIN
    CREATE INDEX IX_Feedbacks_UserId_FeedbackDate ON Feedbacks(UserId, FeedbackDate);
    PRINT '✓ Índice criado: IX_Feedbacks_UserId_FeedbackDate';
END
ELSE
BEGIN
    PRINT '✓ Índice IX_Feedbacks_UserId_FeedbackDate já existe';
END

-- ========================================
-- VERIFICAÇÃO FINAL
-- ========================================
PRINT '';
PRINT '=== VERIFICAÇÃO FINAL ===';

DECLARE @TotalRecords INT, @RecordsWithDate INT;
SELECT @TotalRecords = COUNT(*) FROM Feedbacks;
SELECT @RecordsWithDate = COUNT(*) FROM Feedbacks WHERE FeedbackDate IS NOT NULL;

PRINT 'Total de registros: ' + CAST(@TotalRecords AS VARCHAR(10));
PRINT 'Registros com FeedbackDate: ' + CAST(@RecordsWithDate AS VARCHAR(10));

IF @TotalRecords = @RecordsWithDate
BEGIN
    PRINT '';
    PRINT '🎉 MIGRAÇÃO CONCLUÍDA COM SUCESSO! 🎉';
    PRINT 'Todos os registros foram migrados para o sistema diário.';
END
ELSE
BEGIN
    PRINT '';
    PRINT '⚠️  ATENÇÃO: Alguns registros não foram migrados.';
    PRINT 'Verifique os dados manualmente.';
END

-- Mostrar exemplo dos dados migrados
PRINT '';
PRINT '=== EXEMPLO DOS DADOS MIGRADOS ===';
SELECT TOP 3 
    Id, 
    UserId, 
    FeedbackDate, 
    SatisfactionRating,
    CreatedAt
FROM Feedbacks 
ORDER BY FeedbackDate DESC;
