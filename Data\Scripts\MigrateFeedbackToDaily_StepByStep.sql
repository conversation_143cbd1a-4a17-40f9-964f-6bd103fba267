-- Script de migração passo a passo para converter feedback de semanal para diário
-- Execute cada seção separadamente para maior controle

-- ========================================
-- PASSO 1: CRIAR BACKUP
-- ========================================
SELECT * INTO Feedbacks_Backup FROM Feedbacks;
SELECT 'Backup criado com sucesso' AS Status;

-- ========================================
-- PASSO 2: ADICIONAR COLUNA FeedbackDate
-- ========================================
ALTER TABLE Feedbacks ADD FeedbackDate DATE;
SELECT 'Coluna FeedbackDate adicionada' AS Status;

-- ========================================
-- PASSO 3: MIGRAR DADOS EXISTENTES
-- ========================================
-- Converter WeekNumber/Year para FeedbackDate
-- Assumindo que o feedback foi dado no domingo da semana correspondente
UPDATE Feedbacks 
SET FeedbackDate = DATEADD(WEEK, WeekNumber - 1, DATEFROMPARTS(Year, 1, 1))
WHERE FeedbackDate IS NULL;

SELECT 'Dados migrados para FeedbackDate' AS Status;

-- ========================================
-- PASSO 4: TORNAR FeedbackDate OBRIGATÓRIO
-- ========================================
ALTER TABLE Feedbacks ALTER COLUMN FeedbackDate DATE NOT NULL;
SELECT 'FeedbackDate definido como NOT NULL' AS Status;

-- ========================================
-- PASSO 5: RENOMEAR COLUNA HighlightOfWeek
-- ========================================
EXEC sp_rename 'Feedbacks.HighlightOfWeek', 'HighlightOfDay', 'COLUMN';
SELECT 'Coluna renomeada para HighlightOfDay' AS Status;

-- ========================================
-- PASSO 6: CRIAR CONSTRAINT ÚNICA
-- ========================================
ALTER TABLE Feedbacks ADD CONSTRAINT UQ_Feedbacks_UserDate UNIQUE (UserId, FeedbackDate);
SELECT 'Constraint única criada' AS Status;

-- ========================================
-- PASSO 7: ATUALIZAR ÍNDICES
-- ========================================
-- Remover índice antigo (se existir)
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Feedbacks_WeekNumber_Year')
    DROP INDEX IX_Feedbacks_WeekNumber_Year ON Feedbacks;

-- Criar novos índices
CREATE INDEX IX_Feedbacks_FeedbackDate ON Feedbacks(FeedbackDate);
CREATE INDEX IX_Feedbacks_UserId_FeedbackDate ON Feedbacks(UserId, FeedbackDate);

SELECT 'Índices atualizados' AS Status;

-- ========================================
-- PASSO 8: VERIFICAR MIGRAÇÃO
-- ========================================
SELECT 
    'Verificação da Migração' AS Titulo,
    COUNT(*) AS TotalRegistros
FROM Feedbacks;

SELECT 
    'Registros com FeedbackDate' AS Titulo,
    COUNT(*) AS Quantidade
FROM Feedbacks 
WHERE FeedbackDate IS NOT NULL;

SELECT 
    'Registros com HighlightOfDay' AS Titulo,
    COUNT(*) AS Quantidade
FROM Feedbacks 
WHERE HighlightOfDay IS NOT NULL;

-- ========================================
-- PASSO 9: MOSTRAR EXEMPLOS
-- ========================================
SELECT TOP 5 
    Id, 
    UserId, 
    FeedbackDate, 
    SatisfactionRating,
    CASE 
        WHEN LEN(Comment) > 50 THEN LEFT(Comment, 50) + '...'
        ELSE Comment 
    END AS Comment_Preview,
    CreatedAt
FROM Feedbacks 
ORDER BY FeedbackDate DESC;

SELECT 'Migração concluída com sucesso!' AS Status;
