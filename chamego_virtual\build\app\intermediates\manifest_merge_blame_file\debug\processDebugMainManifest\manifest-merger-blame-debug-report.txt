1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.chamego_virtual"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\android\app\src\main\AndroidManifest.xml:3:5-66
15-->C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\android\app\src\main\AndroidManifest.xml:3:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\android\app\src\main\AndroidManifest.xml:42:5-47:15
24        <intent>
24-->C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\android\app\src\main\AndroidManifest.xml:43:9-46:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\android\app\src\main\AndroidManifest.xml:44:13-72
25-->C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\android\app\src\main\AndroidManifest.xml:44:21-70
26
27            <data android:mimeType="text/plain" />
27-->C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\android\app\src\main\AndroidManifest.xml:45:13-50
27-->C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\android\app\src\main\AndroidManifest.xml:45:19-48
28        </intent>
29    </queries>
30
31    <uses-permission android:name="android.permission.WAKE_LOCK" />
31-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
31-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
32    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Permissions options for the `notification` group -->
32-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
32-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
33    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
33-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
33-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-74
34    <uses-permission android:name="android.permission.VIBRATE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
34-->[:flutter_local_notifications] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
34-->[:flutter_local_notifications] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
35    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
35-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
35-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
36    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
36-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34bbb2a874ce09366d0b76d5a7eca97b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
36-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34bbb2a874ce09366d0b76d5a7eca97b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:22-76
37    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
37-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34bbb2a874ce09366d0b76d5a7eca97b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
37-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34bbb2a874ce09366d0b76d5a7eca97b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
38    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
38-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34bbb2a874ce09366d0b76d5a7eca97b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
38-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34bbb2a874ce09366d0b76d5a7eca97b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
39    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
39-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6172b3d60100aa5c566396626dd93e0\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
39-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6172b3d60100aa5c566396626dd93e0\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
40
41    <permission
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34ae8682f85f5cb1e3f80c2df56ac56d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
42        android:name="com.example.chamego_virtual.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34ae8682f85f5cb1e3f80c2df56ac56d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
43        android:protectionLevel="signature" />
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34ae8682f85f5cb1e3f80c2df56ac56d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
44
45    <uses-permission android:name="com.example.chamego_virtual.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34ae8682f85f5cb1e3f80c2df56ac56d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34ae8682f85f5cb1e3f80c2df56ac56d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
46
47    <application
48        android:name="android.app.Application"
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34ae8682f85f5cb1e3f80c2df56ac56d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
50        android:debuggable="true"
51        android:extractNativeLibs="false"
52        android:icon="@mipmap/ic_launcher"
53        android:label="chamego_virtual" >
54        <activity
55            android:name="com.example.chamego_virtual.MainActivity"
56            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
57            android:exported="true"
58            android:hardwareAccelerated="true"
59            android:launchMode="singleTop"
60            android:taskAffinity=""
61            android:theme="@style/LaunchTheme"
62            android:windowSoftInputMode="adjustResize" >
63
64            <!--
65                 Specifies an Android theme to apply to this Activity as soon as
66                 the Android process has started. This theme is visible to the user
67                 while the Flutter UI initializes. After that, this theme continues
68                 to determine the Window background behind the Flutter UI.
69            -->
70            <meta-data
71                android:name="io.flutter.embedding.android.NormalTheme"
72                android:resource="@style/NormalTheme" />
73
74            <intent-filter>
75                <action android:name="android.intent.action.MAIN" />
76
77                <category android:name="android.intent.category.LAUNCHER" />
78            </intent-filter>
79        </activity>
80        <!--
81             Don't delete the meta-data below.
82             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
83        -->
84        <meta-data
85            android:name="flutterEmbedding"
86            android:value="2" />
87
88        <service
88-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
89            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
89-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
90            android:exported="false"
90-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
91            android:permission="android.permission.BIND_JOB_SERVICE" />
91-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
92        <service
92-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
93            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
93-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
94            android:exported="false" >
94-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
95            <intent-filter>
95-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
96                <action android:name="com.google.firebase.MESSAGING_EVENT" />
96-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
96-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
97            </intent-filter>
98        </service>
99
100        <receiver
100-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
101            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
101-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
102            android:exported="true"
102-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
103            android:permission="com.google.android.c2dm.permission.SEND" >
103-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
104            <intent-filter>
104-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
105                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
105-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
105-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
106            </intent-filter>
107        </receiver>
108
109        <service
109-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
110            android:name="com.google.firebase.components.ComponentDiscoveryService"
110-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
111            android:directBootAware="true"
111-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e6db8b7cb745974480119df1fcb8b44\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
112            android:exported="false" >
112-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
113            <meta-data
113-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
114                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
114-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
116            <meta-data
116-->[:firebase_core] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
117                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
117-->[:firebase_core] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[:firebase_core] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
119            <meta-data
119-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
120                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
120-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
122            <meta-data
122-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
123                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
123-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
125            <meta-data
125-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34bbb2a874ce09366d0b76d5a7eca97b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
126                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
126-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34bbb2a874ce09366d0b76d5a7eca97b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34bbb2a874ce09366d0b76d5a7eca97b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
128            <meta-data
128-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d54812e3b56faf50558af581af51bf7f\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
129                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
129-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d54812e3b56faf50558af581af51bf7f\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d54812e3b56faf50558af581af51bf7f\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
131            <meta-data
131-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d54812e3b56faf50558af581af51bf7f\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
132                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
132-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d54812e3b56faf50558af581af51bf7f\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
133                android:value="com.google.firebase.components.ComponentRegistrar" />
133-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d54812e3b56faf50558af581af51bf7f\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
134            <meta-data
134-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8423ccbf0cf850b0427b92c5ef37647b\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
135                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
135-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8423ccbf0cf850b0427b92c5ef37647b\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
136                android:value="com.google.firebase.components.ComponentRegistrar" />
136-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8423ccbf0cf850b0427b92c5ef37647b\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
137            <meta-data
137-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e6db8b7cb745974480119df1fcb8b44\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
138                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
138-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e6db8b7cb745974480119df1fcb8b44\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
139                android:value="com.google.firebase.components.ComponentRegistrar" />
139-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e6db8b7cb745974480119df1fcb8b44\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
140            <meta-data
140-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66f300294a808dc82d3d14f8d13b7544\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
141                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
141-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66f300294a808dc82d3d14f8d13b7544\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
142                android:value="com.google.firebase.components.ComponentRegistrar" />
142-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66f300294a808dc82d3d14f8d13b7544\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
143        </service>
144
145        <provider
145-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
146            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
146-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
147            android:authorities="com.example.chamego_virtual.flutterfirebasemessaginginitprovider"
147-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
148            android:exported="false"
148-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
149            android:initOrder="99" />
149-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
150
151        <receiver
151-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
152            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
152-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
153            android:exported="true"
153-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
154            android:permission="com.google.android.c2dm.permission.SEND" >
154-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
155            <intent-filter>
155-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
156                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
156-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
156-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
157            </intent-filter>
158
159            <meta-data
159-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
160                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
160-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
161                android:value="true" />
161-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
162        </receiver>
163        <!--
164             FirebaseMessagingService performs security checks at runtime,
165             but set to not exported to explicitly avoid allowing another app to call it.
166        -->
167        <service
167-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
168            android:name="com.google.firebase.messaging.FirebaseMessagingService"
168-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
169            android:directBootAware="true"
169-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
170            android:exported="false" >
170-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
171            <intent-filter android:priority="-500" >
171-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
172                <action android:name="com.google.firebase.MESSAGING_EVENT" />
172-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
172-->[:firebase_messaging] C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
173            </intent-filter>
174        </service>
175
176        <uses-library
176-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65506c5965db684cd3fe458829c1a269\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
177            android:name="androidx.window.extensions"
177-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65506c5965db684cd3fe458829c1a269\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
178            android:required="false" />
178-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65506c5965db684cd3fe458829c1a269\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
179        <uses-library
179-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65506c5965db684cd3fe458829c1a269\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
180            android:name="androidx.window.sidecar"
180-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65506c5965db684cd3fe458829c1a269\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
181            android:required="false" />
181-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65506c5965db684cd3fe458829c1a269\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
182
183        <provider
183-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e6db8b7cb745974480119df1fcb8b44\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
184            android:name="com.google.firebase.provider.FirebaseInitProvider"
184-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e6db8b7cb745974480119df1fcb8b44\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
185            android:authorities="com.example.chamego_virtual.firebaseinitprovider"
185-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e6db8b7cb745974480119df1fcb8b44\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
186            android:directBootAware="true"
186-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e6db8b7cb745974480119df1fcb8b44\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
187            android:exported="false"
187-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e6db8b7cb745974480119df1fcb8b44\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
188            android:initOrder="100" />
188-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e6db8b7cb745974480119df1fcb8b44\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
189
190        <receiver
190-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6172b3d60100aa5c566396626dd93e0\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
191            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
191-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6172b3d60100aa5c566396626dd93e0\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
192            android:enabled="true"
192-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6172b3d60100aa5c566396626dd93e0\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
193            android:exported="false" >
193-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6172b3d60100aa5c566396626dd93e0\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
194        </receiver>
195
196        <service
196-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6172b3d60100aa5c566396626dd93e0\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
197            android:name="com.google.android.gms.measurement.AppMeasurementService"
197-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6172b3d60100aa5c566396626dd93e0\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
198            android:enabled="true"
198-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6172b3d60100aa5c566396626dd93e0\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
199            android:exported="false" />
199-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6172b3d60100aa5c566396626dd93e0\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
200        <service
200-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6172b3d60100aa5c566396626dd93e0\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
201            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
201-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6172b3d60100aa5c566396626dd93e0\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
202            android:enabled="true"
202-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6172b3d60100aa5c566396626dd93e0\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
203            android:exported="false"
203-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6172b3d60100aa5c566396626dd93e0\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
204            android:permission="android.permission.BIND_JOB_SERVICE" />
204-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6172b3d60100aa5c566396626dd93e0\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
205
206        <activity
206-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21c8db8decf765cf7dd74ee06345bde5\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
207            android:name="com.google.android.gms.common.api.GoogleApiActivity"
207-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21c8db8decf765cf7dd74ee06345bde5\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
208            android:exported="false"
208-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21c8db8decf765cf7dd74ee06345bde5\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
209            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
209-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21c8db8decf765cf7dd74ee06345bde5\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
210
211        <meta-data
211-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c3eefca2d9a5f42152c30d1f581b28f\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
212            android:name="com.google.android.gms.version"
212-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c3eefca2d9a5f42152c30d1f581b28f\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
213            android:value="@integer/google_play_services_version" />
213-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c3eefca2d9a5f42152c30d1f581b28f\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
214
215        <uses-library
215-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd38b806ba982e4d920f11c03249e664\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
216            android:name="android.ext.adservices"
216-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd38b806ba982e4d920f11c03249e664\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
217            android:required="false" />
217-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd38b806ba982e4d920f11c03249e664\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
218
219        <provider
219-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3a01c48789496b004a78e0db47ba99\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
220            android:name="androidx.startup.InitializationProvider"
220-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3a01c48789496b004a78e0db47ba99\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
221            android:authorities="com.example.chamego_virtual.androidx-startup"
221-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3a01c48789496b004a78e0db47ba99\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
222            android:exported="false" >
222-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3a01c48789496b004a78e0db47ba99\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
223            <meta-data
223-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3a01c48789496b004a78e0db47ba99\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
224                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
224-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3a01c48789496b004a78e0db47ba99\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
225                android:value="androidx.startup" />
225-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3a01c48789496b004a78e0db47ba99\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
226            <meta-data
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
227                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
228                android:value="androidx.startup" />
228-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
229        </provider>
230
231        <service
231-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b7b73e845f365441f328289933eb58e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
232            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
232-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b7b73e845f365441f328289933eb58e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
233            android:exported="false" >
233-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b7b73e845f365441f328289933eb58e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
234            <meta-data
234-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b7b73e845f365441f328289933eb58e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
235                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
235-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b7b73e845f365441f328289933eb58e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
236                android:value="cct" />
236-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b7b73e845f365441f328289933eb58e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
237        </service>
238        <service
238-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8929dc31879e17749bfec75f8643b673\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
239            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
239-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8929dc31879e17749bfec75f8643b673\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
240            android:exported="false"
240-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8929dc31879e17749bfec75f8643b673\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
241            android:permission="android.permission.BIND_JOB_SERVICE" >
241-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8929dc31879e17749bfec75f8643b673\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
242        </service>
243
244        <receiver
244-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8929dc31879e17749bfec75f8643b673\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
245            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
245-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8929dc31879e17749bfec75f8643b673\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
246            android:exported="false" />
246-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8929dc31879e17749bfec75f8643b673\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
247        <receiver
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
248            android:name="androidx.profileinstaller.ProfileInstallReceiver"
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
249            android:directBootAware="false"
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
250            android:enabled="true"
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
251            android:exported="true"
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
252            android:permission="android.permission.DUMP" >
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
253            <intent-filter>
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
254                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
255            </intent-filter>
256            <intent-filter>
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
257                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
258            </intent-filter>
259            <intent-filter>
259-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
260                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
261            </intent-filter>
262            <intent-filter>
262-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
263                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
264            </intent-filter>
265        </receiver>
266    </application>
267
268</manifest>
