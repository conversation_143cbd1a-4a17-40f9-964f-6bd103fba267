import 'package:flutter/material.dart';
import '../config/app_config.dart';
import '../services/api_service.dart';

class EnvironmentConfigScreen extends StatefulWidget {
  const EnvironmentConfigScreen({super.key});

  @override
  State<EnvironmentConfigScreen> createState() => _EnvironmentConfigScreenState();
}

class _EnvironmentConfigScreenState extends State<EnvironmentConfigScreen> {
  bool _isTestingConnection = false;
  Map<String, dynamic>? _connectionResult;

  Future<void> _testConnection() async {
    setState(() {
      _isTestingConnection = true;
      _connectionResult = null;
    });

    try {
      final apiService = ApiService();
      final result = await apiService.checkConnection();
      setState(() {
        _connectionResult = result;
      });
    } catch (e) {
      setState(() {
        _connectionResult = {
          'success': false,
          'message': 'Erro ao testar conexão',
          'error': e.toString(),
        };
      });
    } finally {
      setState(() {
        _isTestingConnection = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Configuração de Ambiente'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Informações do ambiente atual
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Ambiente Atual',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow('Ambiente:', AppConfig.currentEnvironment.name),
                    _buildInfoRow('URL Base:', AppConfig.baseUrl),
                    _buildInfoRow('Modo Debug:', AppConfig.isDevelopment ? 'Ativado' : 'Desativado'),
                    _buildInfoRow('Timeout API:', '${AppConfig.apiTimeout.inSeconds}s'),
                    _buildInfoRow('Timeout Conexão:', '${AppConfig.connectionTimeout.inSeconds}s'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Descrição do ambiente
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Descrição',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      AppConfig.currentEnvironment.description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Teste de conexão
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Teste de Conexão',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    
                    ElevatedButton(
                      onPressed: _isTestingConnection ? null : _testConnection,
                      child: _isTestingConnection
                          ? const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                ),
                                SizedBox(width: 8),
                                Text('Testando...'),
                              ],
                            )
                          : const Text('Testar Conexão'),
                    ),
                    
                    if (_connectionResult != null) ...[
                      const SizedBox(height: 16),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: _connectionResult!['success'] 
                              ? Colors.green.shade50 
                              : Colors.red.shade50,
                          border: Border.all(
                            color: _connectionResult!['success'] 
                                ? Colors.green 
                                : Colors.red,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  _connectionResult!['success'] 
                                      ? Icons.check_circle 
                                      : Icons.error,
                                  color: _connectionResult!['success'] 
                                      ? Colors.green 
                                      : Colors.red,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  _connectionResult!['success'] 
                                      ? 'Conexão Bem-sucedida' 
                                      : 'Falha na Conexão',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    color: _connectionResult!['success'] 
                                        ? Colors.green.shade700 
                                        : Colors.red.shade700,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _connectionResult!['message'] ?? 'Sem mensagem',
                              style: const TextStyle(fontSize: 14),
                            ),
                            if (_connectionResult!['statusCode'] != null) ...[
                              const SizedBox(height: 4),
                              Text(
                                'Status Code: ${_connectionResult!['statusCode']}',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.black54,
                                ),
                              ),
                            ],
                            if (_connectionResult!['error'] != null) ...[
                              const SizedBox(height: 4),
                              Text(
                                'Erro: ${_connectionResult!['error']}',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.red.shade700,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const Spacer(),
            
            // Instruções para trocar ambiente
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue.shade700),
                        const SizedBox(width: 8),
                        Text(
                          'Como trocar o ambiente',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Para trocar o ambiente, edite o arquivo:\n'
                      'lib/config/app_config.dart\n\n'
                      'Altere a linha:\n'
                      'static const Environment currentEnvironment = Environment.development;\n\n'
                      'Para o ambiente desejado e reinicie o app.',
                      style: TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.black54,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
