import 'dart:io';
import 'package:flutter/material.dart';
import 'package:chamego_virtual/models/user.dart';
import 'package:chamego_virtual/services/auth_service.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();

  AuthResponse? _authResponse;
  bool _isLoading = false;
  String? _error;

  AuthResponse? get authResponse => _authResponse;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _authResponse != null;

  // Método para verificar se o usuário está autenticado ao iniciar o app
  Future<void> checkAuthentication() async {
    _isLoading = true;
    notifyListeners();

    try {
      final isAuth = await _authService.isAuthenticated();
      if (!isAuth) {
        _authResponse = null;
      }
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Método para registrar um novo usuário
  Future<bool> register(String username, String email, String password, String? displayName, {int? partnerId}) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _authResponse = await _authService.register(username, email, password, displayName, partnerId: partnerId);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = _getErrorMessage(e);
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Método para fazer login
  Future<bool> login(String username, String password) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _authResponse = await _authService.login(username, password);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = _getErrorMessage(e);
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Método para fazer logout
  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _authService.logout();
      _authResponse = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Método para limpar erros
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Método para converter erros em mensagens amigáveis
  String _getErrorMessage(dynamic error) {
    String errorString = error.toString();

    // Erro de conexão recusada
    if (errorString.contains('Connection refused') ||
        errorString.contains('SocketException')) {
      return 'Não foi possível conectar ao servidor. Verifique se:\n'
             '• O servidor está rodando\n'
             '• Você está conectado à internet\n'
             '• O dispositivo está na mesma rede (se usando servidor local)';
    }

    // Erro de timeout
    if (errorString.contains('TimeoutException') ||
        errorString.contains('timeout')) {
      return 'Tempo limite de conexão esgotado. Verifique sua conexão com a internet.';
    }

    // Erro de formato de resposta
    if (errorString.contains('FormatException') ||
        errorString.contains('Unexpected character')) {
      return 'Erro na comunicação com o servidor. Tente novamente.';
    }

    // Erro HTTP específico
    if (errorString.contains('Exception: Falha')) {
      return errorString.replaceAll('Exception: ', '');
    }

    // Erro genérico
    return 'Erro inesperado. Tente novamente em alguns instantes.';
  }
}
