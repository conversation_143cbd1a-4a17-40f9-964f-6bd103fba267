using Dapper;
using ProjetoChamegoes.Models.Entities;

namespace ProjetoChamegoes.Data.Repositories;

public interface IFeedbackRepository : IRepository<Feedback>
{
    Task<IEnumerable<Feedback>> GetByUserIdAsync(int userId);
    Task<Feedback?> GetLatestByUserIdAsync(int userId);
    Task<bool> ExistsForDateAsync(int userId, DateTime feedbackDate);
    Task<Feedback?> GetByUserIdAndDateAsync(int userId, DateTime feedbackDate);
    Task<IEnumerable<Feedback>> GetByUserIdAndDateRangeAsync(int userId, DateTime startDate, DateTime endDate);
}

public class FeedbackRepository : IFeedbackRepository
{
    private readonly IDbConnectionFactory _connectionFactory;

    public FeedbackRepository(IDbConnectionFactory connectionFactory)
    {
        _connectionFactory = connectionFactory;
    }

    public async Task<IEnumerable<Feedback>> GetAllAsync()
    {
        using var connection = _connectionFactory.CreateConnection();
        return await connection.QueryAsync<Feedback>("SELECT * FROM Feedbacks");
    }

    public async Task<Feedback?> GetByIdAsync(int id)
    {
        using var connection = _connectionFactory.CreateConnection();
        return await connection.QuerySingleOrDefaultAsync<Feedback>(
            "SELECT * FROM Feedbacks WHERE Id = @Id", 
            new { Id = id });
    }

    public async Task<IEnumerable<Feedback>> GetByUserIdAsync(int userId)
    {
        using var connection = _connectionFactory.CreateConnection();
        return await connection.QueryAsync<Feedback>(
            "SELECT * FROM Feedbacks WHERE UserId = @UserId ORDER BY FeedbackDate DESC",
            new { UserId = userId });
    }

    public async Task<Feedback?> GetLatestByUserIdAsync(int userId)
    {
        using var connection = _connectionFactory.CreateConnection();
        return await connection.QuerySingleOrDefaultAsync<Feedback>(
            "SELECT TOP 1 * FROM Feedbacks WHERE UserId = @UserId ORDER BY FeedbackDate DESC",
            new { UserId = userId });
    }

    public async Task<bool> ExistsForDateAsync(int userId, DateTime feedbackDate)
    {
        using var connection = _connectionFactory.CreateConnection();
        return await connection.ExecuteScalarAsync<bool>(
            "SELECT COUNT(1) FROM Feedbacks WHERE UserId = @UserId AND FeedbackDate = @FeedbackDate",
            new { UserId = userId, FeedbackDate = feedbackDate.Date });
    }

    public async Task<Feedback?> GetByUserIdAndDateAsync(int userId, DateTime feedbackDate)
    {
        using var connection = _connectionFactory.CreateConnection();
        return await connection.QuerySingleOrDefaultAsync<Feedback>(
            "SELECT * FROM Feedbacks WHERE UserId = @UserId AND FeedbackDate = @FeedbackDate",
            new { UserId = userId, FeedbackDate = feedbackDate.Date });
    }

    public async Task<IEnumerable<Feedback>> GetByUserIdAndDateRangeAsync(int userId, DateTime startDate, DateTime endDate)
    {
        using var connection = _connectionFactory.CreateConnection();
        return await connection.QueryAsync<Feedback>(
            "SELECT * FROM Feedbacks WHERE UserId = @UserId AND FeedbackDate >= @StartDate AND FeedbackDate <= @EndDate ORDER BY FeedbackDate DESC",
            new { UserId = userId, StartDate = startDate.Date, EndDate = endDate.Date });
    }

    public async Task<int> CreateAsync(Feedback feedback)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = @"
            INSERT INTO Feedbacks (UserId, SatisfactionRating, Comment, HighlightOfDay, ImprovementSuggestion, CreatedAt, FeedbackDate)
            VALUES (@UserId, @SatisfactionRating, @Comment, @HighlightOfDay, @ImprovementSuggestion, @CreatedAt, @FeedbackDate);
            SELECT CAST(SCOPE_IDENTITY() as int)";

        return await connection.QuerySingleAsync<int>(sql, feedback);
    }

    public async Task<bool> UpdateAsync(Feedback feedback)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = @"
            UPDATE Feedbacks
            SET SatisfactionRating = @SatisfactionRating,
                Comment = @Comment,
                HighlightOfDay = @HighlightOfDay,
                ImprovementSuggestion = @ImprovementSuggestion
            WHERE Id = @Id";

        var rowsAffected = await connection.ExecuteAsync(sql, feedback);
        return rowsAffected > 0;
    }

    public async Task<bool> DeleteAsync(int id)
    {
        using var connection = _connectionFactory.CreateConnection();
        var rowsAffected = await connection.ExecuteAsync(
            "DELETE FROM Feedbacks WHERE Id = @Id", 
            new { Id = id });
        
        return rowsAffected > 0;
    }
}
