-- Script de migração para converter feedback de semanal para diário
-- <PERSON><PERSON> script atualiza a estrutura da tabela Feedbacks e migra os dados existentes

-- IMPORTANTE: Faça backup da tabela antes de executar este script!

-- 1. <PERSON><PERSON><PERSON> backup da tabela original
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Feedbacks_Backup')
BEGIN
    SELECT * INTO Feedbacks_Backup FROM Feedbacks;
    PRINT 'Backup da tabela Feedbacks criado como Feedbacks_Backup';
END

-- 2. Adicionar nova coluna FeedbackDate
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'FeedbackDate')
BEGIN
    ALTER TABLE Feedbacks ADD FeedbackDate DATE;
    PRINT 'Coluna FeedbackDate adicionada';
END

-- 3. Migrar dados existentes: converter WeekNumber/Year para FeedbackDate
-- Assumindo que o feedback foi dado no domingo da semana correspondente
UPDATE Feedbacks 
SET FeedbackDate = DATEADD(WEEK, WeekNumber - 1, DATEFROMPARTS(Year, 1, 1))
WHERE FeedbackDate IS NULL;

PRINT 'Dados migrados: WeekNumber/Year convertidos para FeedbackDate';

-- 4. Tornar FeedbackDate obrigatório
ALTER TABLE Feedbacks ALTER COLUMN FeedbackDate DATE NOT NULL;
PRINT 'Coluna FeedbackDate definida como NOT NULL';

-- 5. Renomear coluna HighlightOfWeek para HighlightOfDay
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'HighlightOfWeek')
AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'HighlightOfDay')
BEGIN
    EXEC sp_rename 'Feedbacks.HighlightOfWeek', 'HighlightOfDay', 'COLUMN';
    PRINT 'Coluna HighlightOfWeek renomeada para HighlightOfDay';
END

-- 6. Criar constraint única para UserId + FeedbackDate (um feedback por dia por usuário)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'UQ_Feedbacks_UserDate')
BEGIN
    ALTER TABLE Feedbacks ADD CONSTRAINT UQ_Feedbacks_UserDate UNIQUE (UserId, FeedbackDate);
    PRINT 'Constraint única criada para UserId + FeedbackDate';
END

-- 7. Remover colunas antigas (WeekNumber, Year) - CUIDADO: Isso remove dados permanentemente!
-- Descomente as linhas abaixo apenas se tiver certeza de que não precisa mais dos dados antigos

/*
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'WeekNumber')
BEGIN
    ALTER TABLE Feedbacks DROP COLUMN WeekNumber;
    PRINT 'Coluna WeekNumber removida';
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'Year')
BEGIN
    ALTER TABLE Feedbacks DROP COLUMN Year;
    PRINT 'Coluna Year removida';
END
*/

-- 8. Atualizar índices existentes
-- Remover índices antigos baseados em WeekNumber/Year
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Feedbacks_WeekNumber_Year')
BEGIN
    DROP INDEX IX_Feedbacks_WeekNumber_Year ON Feedbacks;
    PRINT 'Índice IX_Feedbacks_WeekNumber_Year removido';
END

-- Criar novo índice baseado em FeedbackDate
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Feedbacks_FeedbackDate')
BEGIN
    CREATE INDEX IX_Feedbacks_FeedbackDate ON Feedbacks(FeedbackDate);
    PRINT 'Índice IX_Feedbacks_FeedbackDate criado';
END

-- Criar índice composto para consultas por usuário e data
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Feedbacks_UserId_FeedbackDate')
BEGIN
    CREATE INDEX IX_Feedbacks_UserId_FeedbackDate ON Feedbacks(UserId, FeedbackDate);
    PRINT 'Índice IX_Feedbacks_UserId_FeedbackDate criado';
END

-- 9. Verificar a migração
PRINT '=== VERIFICAÇÃO DA MIGRAÇÃO ===';

DECLARE @TotalRecords INT, @RecordsWithDate INT, @RecordsWithHighlight INT;
SELECT @TotalRecords = COUNT(*) FROM Feedbacks;
SELECT @RecordsWithDate = COUNT(*) FROM Feedbacks WHERE FeedbackDate IS NOT NULL;
SELECT @RecordsWithHighlight = COUNT(*) FROM Feedbacks WHERE HighlightOfDay IS NOT NULL;

PRINT 'Total de registros na tabela Feedbacks: ' + CAST(@TotalRecords AS VARCHAR(10));
PRINT 'Registros com FeedbackDate preenchido: ' + CAST(@RecordsWithDate AS VARCHAR(10));
PRINT 'Registros com HighlightOfDay: ' + CAST(@RecordsWithHighlight AS VARCHAR(10));

-- Mostrar alguns exemplos dos dados migrados
PRINT '=== EXEMPLOS DE DADOS MIGRADOS ===';
SELECT TOP 5 
    Id, 
    UserId, 
    FeedbackDate, 
    SatisfactionRating,
    CASE 
        WHEN LEN(Comment) > 50 THEN LEFT(Comment, 50) + '...'
        ELSE Comment 
    END AS Comment_Preview,
    CreatedAt
FROM Feedbacks 
ORDER BY FeedbackDate DESC;

PRINT '=== MIGRAÇÃO CONCLUÍDA ===';
PRINT 'A tabela Feedbacks foi migrada com sucesso para o sistema diário.';
PRINT 'Backup disponível na tabela Feedbacks_Backup.';
PRINT 'Para remover as colunas antigas (WeekNumber, Year), descomente e execute a seção 7 do script.';
