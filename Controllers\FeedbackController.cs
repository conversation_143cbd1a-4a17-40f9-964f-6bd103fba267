using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjetoChamegoes.Data.Repositories;
using ProjetoChamegoes.Helpers;
using ProjetoChamegoes.Models.DTOs;
using ProjetoChamegoes.Models.Entities;
using System.Security.Claims;

namespace ProjetoChamegoes.Controllers;

[Authorize]
[ApiController]
[Route("api/feedback")]
public class FeedbackController : ControllerBase
{
    private readonly IFeedbackRepository _feedbackRepository;
    private readonly IUserRepository _userRepository;

    public FeedbackController(IFeedbackRepository feedbackRepository, IUserRepository userRepository)
    {
        _feedbackRepository = feedbackRepository;
        _userRepository = userRepository;
    }

    [HttpPost]
    public async Task<ActionResult<FeedbackDTO>> CreateFeedback(CreateFeedbackDTO createDto)
    {
        // Obter ID do usuário atual
        var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
        if (userId == 0)
        {
            return Unauthorized();
        }

        // Determinar a data do feedback (se não informada, usa a data atual)
        var feedbackDate = createDto.FeedbackDate?.Date ?? DateTime.Now.Date;

        // Verificar se já existe feedback para esta data
        var existsForDate = await _feedbackRepository.ExistsForDateAsync(userId, feedbackDate);

        if (existsForDate)
        {
            return BadRequest("Você já enviou feedback para esta data");
        }

        // Criar novo feedback
        var feedback = new Feedback
        {
            UserId = userId,
            SatisfactionRating = createDto.SatisfactionRating,
            Comment = createDto.Comment,
            HighlightOfDay = createDto.HighlightOfDay,
            ImprovementSuggestion = createDto.ImprovementSuggestion,
            CreatedAt = DateTime.UtcNow,
            FeedbackDate = feedbackDate
        };

        // Adicionar ao banco de dados
        feedback.Id = await _feedbackRepository.CreateAsync(feedback);

        // Buscar usuário
        var user = await _userRepository.GetByIdAsync(userId);

        // Retornar DTO
        return Ok(new FeedbackDTO
        {
            Id = feedback.Id,
            UserId = feedback.UserId,
            Username = user?.Username ?? "Usuário",
            SatisfactionRating = feedback.SatisfactionRating,
            Comment = feedback.Comment,
            HighlightOfDay = feedback.HighlightOfDay,
            ImprovementSuggestion = feedback.ImprovementSuggestion,
            CreatedAt = feedback.CreatedAt,
            FeedbackDate = feedback.FeedbackDate
        });
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<FeedbackDTO>>> GetFeedbacks()
    {
        // Obter ID do usuário atual
        var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
        if (userId == 0)
        {
            return Unauthorized();
        }

        // Buscar feedbacks do usuário
        var feedbacks = await _feedbackRepository.GetByUserIdAsync(userId);

        // Mapear para DTOs
        var feedbackDtos = feedbacks.Select(f => new FeedbackDTO
        {
            Id = f.Id,
            UserId = f.UserId,
            Username = User.FindFirst(ClaimTypes.Name)?.Value ?? "Usuário",
            SatisfactionRating = f.SatisfactionRating,
            Comment = f.Comment,
            HighlightOfDay = f.HighlightOfDay,
            ImprovementSuggestion = f.ImprovementSuggestion,
            CreatedAt = f.CreatedAt,
            FeedbackDate = f.FeedbackDate
        });

        return Ok(feedbackDtos);
    }

    [HttpGet("latest")]
    public async Task<ActionResult<FeedbackDTO>> GetLatestFeedback()
    {
        // Obter ID do usuário atual
        var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
        if (userId == 0)
        {
            return Unauthorized();
        }

        // Buscar feedback mais recente
        var feedback = await _feedbackRepository.GetLatestByUserIdAsync(userId);

        if (feedback == null)
        {
            return NotFound("Nenhum feedback encontrado");
        }

        // Retornar DTO
        return Ok(new FeedbackDTO
        {
            Id = feedback.Id,
            UserId = feedback.UserId,
            Username = User.FindFirst(ClaimTypes.Name)?.Value ?? "Usuário",
            SatisfactionRating = feedback.SatisfactionRating,
            Comment = feedback.Comment,
            HighlightOfDay = feedback.HighlightOfDay,
            ImprovementSuggestion = feedback.ImprovementSuggestion,
            CreatedAt = feedback.CreatedAt,
            FeedbackDate = feedback.FeedbackDate
        });
    }

    [HttpGet("today")]
    public async Task<ActionResult<FeedbackDTO>> GetTodayFeedback()
    {
        // Obter ID do usuário atual
        var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
        if (userId == 0)
        {
            return Unauthorized();
        }

        // Buscar feedback de hoje
        var feedback = await _feedbackRepository.GetByUserIdAndDateAsync(userId, DateTime.Now.Date);

        if (feedback == null)
        {
            return NotFound("Nenhum feedback encontrado para hoje");
        }

        // Retornar DTO
        return Ok(new FeedbackDTO
        {
            Id = feedback.Id,
            UserId = feedback.UserId,
            Username = User.FindFirst(ClaimTypes.Name)?.Value ?? "Usuário",
            SatisfactionRating = feedback.SatisfactionRating,
            Comment = feedback.Comment,
            HighlightOfDay = feedback.HighlightOfDay,
            ImprovementSuggestion = feedback.ImprovementSuggestion,
            CreatedAt = feedback.CreatedAt,
            FeedbackDate = feedback.FeedbackDate
        });
    }

    [HttpGet("date/{date}")]
    public async Task<ActionResult<FeedbackDTO>> GetFeedbackByDate(DateTime date)
    {
        // Obter ID do usuário atual
        var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
        if (userId == 0)
        {
            return Unauthorized();
        }

        // Buscar feedback da data específica
        var feedback = await _feedbackRepository.GetByUserIdAndDateAsync(userId, date.Date);

        if (feedback == null)
        {
            return NotFound($"Nenhum feedback encontrado para a data {date:dd/MM/yyyy}");
        }

        // Retornar DTO
        return Ok(new FeedbackDTO
        {
            Id = feedback.Id,
            UserId = feedback.UserId,
            Username = User.FindFirst(ClaimTypes.Name)?.Value ?? "Usuário",
            SatisfactionRating = feedback.SatisfactionRating,
            Comment = feedback.Comment,
            HighlightOfDay = feedback.HighlightOfDay,
            ImprovementSuggestion = feedback.ImprovementSuggestion,
            CreatedAt = feedback.CreatedAt,
            FeedbackDate = feedback.FeedbackDate
        });
    }

    [HttpGet("range")]
    public async Task<ActionResult<IEnumerable<FeedbackDTO>>> GetFeedbacksByDateRange([FromQuery] DateTime startDate, [FromQuery] DateTime endDate)
    {
        // Obter ID do usuário atual
        var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
        if (userId == 0)
        {
            return Unauthorized();
        }

        // Buscar feedbacks no período
        var feedbacks = await _feedbackRepository.GetByUserIdAndDateRangeAsync(userId, startDate.Date, endDate.Date);

        // Mapear para DTOs
        var feedbackDtos = feedbacks.Select(f => new FeedbackDTO
        {
            Id = f.Id,
            UserId = f.UserId,
            Username = User.FindFirst(ClaimTypes.Name)?.Value ?? "Usuário",
            SatisfactionRating = f.SatisfactionRating,
            Comment = f.Comment,
            HighlightOfDay = f.HighlightOfDay,
            ImprovementSuggestion = f.ImprovementSuggestion,
            CreatedAt = f.CreatedAt,
            FeedbackDate = f.FeedbackDate
        });

        return Ok(feedbackDtos);
    }
}
