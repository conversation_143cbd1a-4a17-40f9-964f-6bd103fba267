import 'package:chamego_virtual/models/feedback.dart';
import 'package:chamego_virtual/services/api_service.dart';

class FeedbackService {
  final ApiService _apiService = ApiService();
  
  // Método para obter todos os feedbacks
  Future<List<Feedback>> getFeedbacks() async {
    final response = await _apiService.get('feedback');
    return (response as List).map((item) => Feedback.fromJson(item)).toList();
  }
  
  // Método para obter o feedback mais recente
  Future<Feedback?> getLatestFeedback() async {
    try {
      final response = await _apiService.get('feedback/latest');
      return Feedback.fromJson(response);
    } catch (e) {
      // Retorna null se não houver feedback
      return null;
    }
  }
  
  // Método para obter feedback de hoje
  Future<Feedback?> getTodayFeedback() async {
    try {
      final response = await _apiService.get('feedback/today');
      return Feedback.fromJson(response);
    } catch (e) {
      // Retorna null se não houver feedback para hoje
      return null;
    }
  }

  // Método para obter feedback de uma data específica
  Future<Feedback?> getFeedbackByDate(DateTime date) async {
    try {
      final dateString = date.toIso8601String().split('T')[0]; // YYYY-MM-DD
      final response = await _apiService.get('feedback/date/$dateString');
      return Feedback.fromJson(response);
    } catch (e) {
      // Retorna null se não houver feedback para a data
      return null;
    }
  }

  // Método para obter feedbacks em um período
  Future<List<Feedback>> getFeedbacksByDateRange(DateTime startDate, DateTime endDate) async {
    final startDateString = startDate.toIso8601String().split('T')[0];
    final endDateString = endDate.toIso8601String().split('T')[0];

    final response = await _apiService.get('feedback/range?startDate=$startDateString&endDate=$endDateString');
    return (response as List).map((item) => Feedback.fromJson(item)).toList();
  }

  // Método para criar um novo feedback
  Future<Feedback> createFeedback(
    int satisfactionRating,
    String comment,
    String? highlightOfDay,
    String? improvementSuggestion,
    {DateTime? feedbackDate}
  ) async {
    final data = {
      'satisfactionRating': satisfactionRating,
      'comment': comment,
      'highlightOfDay': highlightOfDay,
      'improvementSuggestion': improvementSuggestion,
    };

    // Adicionar data do feedback se fornecida
    if (feedbackDate != null) {
      data['feedbackDate'] = feedbackDate.toIso8601String();
    }

    final response = await _apiService.post('feedback', data);
    return Feedback.fromJson(response);
  }
}
