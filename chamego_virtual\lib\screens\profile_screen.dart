import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:chamego_virtual/models/user.dart';
import 'package:chamego_virtual/providers/auth_provider.dart';
import 'package:chamego_virtual/services/user_service.dart';
import 'package:chamego_virtual/config/app_config.dart';
import 'package:chamego_virtual/screens/environment_config_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final UserService _userService = UserService();
  User? _currentUser;
  User? _partner;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Carregar informações do usuário atual
      try {
        final currentUser = await _userService.getCurrentUser();
        setState(() {
          _currentUser = currentUser;
        });
      } catch (e) {
        Fluttertoast.showToast(
          msg: "Erro ao carregar dados do usuário: $e",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
      }

      // Carregar informações do parceiro (pode ser null se não houver parceiro)
      try {
        final partner = await _userService.getPartner();
        setState(() {
          _partner = partner;
        });
      } catch (e) {
        // Erro ao carregar parceiro não é crítico, apenas definir como null
        setState(() {
          _partner = null;
        });
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      Fluttertoast.showToast(
        msg: "Erro ao carregar dados: $e",
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  // Método para construir o corpo da tela
  Widget _buildBody(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);

    return RefreshIndicator(
      onRefresh: _loadData,
      color: const Color(0xFF7ECBDE),
      child: _isLoading
          ? const Center(child: CircularProgressIndicator(color: Color(0xFF7ECBDE)))
          : SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Seção de perfil do usuário
                  Card(
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                      side: const BorderSide(color: Color(0xFFEEF2F6), width: 1),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              CircleAvatar(
                                radius: 40,
                                backgroundColor: const Color(0xFFF5F8FA),
                                child: Text(
                                  _currentUser?.displayName?.substring(0, 1).toUpperCase() ??
                                      _currentUser?.username.substring(0, 1).toUpperCase() ??
                                      'U',
                                  style: const TextStyle(
                                    fontSize: 30,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xFF7ECBDE),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      _currentUser?.displayName ?? _currentUser?.username ?? 'Usuário',
                                      style: const TextStyle(
                                        fontSize: 22,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                    Text(
                                      _currentUser?.email ?? '',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        color: Colors.black54,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 24),
                          const Text(
                            'Informações do Usuário',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: const Color(0xFFF5F8FA),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: const Color(0xFFEEF2F6)),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Nome:',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      _currentUser?.displayName ?? _currentUser?.username ?? 'Usuário',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Colors.black87,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Email:',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      _currentUser?.email ?? '',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Colors.black87,
                                      ),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'ID:',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Text(
                                            '${authProvider.authResponse?.userId ?? ""}',
                                            style: const TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w600,
                                              color: Color(0xFF7ECBDE),
                                            ),
                                            overflow: TextOverflow.visible,
                                            softWrap: true,
                                          ),
                                        ),
                                        IconButton(
                                          icon: const Icon(Icons.copy, color: Color(0xFF7ECBDE), size: 20),
                                          onPressed: () {
                                            Clipboard.setData(ClipboardData(
                                                text: '${authProvider.authResponse?.userId ?? ""}'));
                                            Fluttertoast.showToast(
                                              msg: "ID copiado para a área de transferência",
                                              toastLength: Toast.LENGTH_SHORT,
                                              gravity: ToastGravity.BOTTOM,
                                              backgroundColor: Colors.green,
                                              textColor: Colors.white,
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Seção de parceiro
                  Card(
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                      side: const BorderSide(color: Color(0xFFEEF2F6), width: 1),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Seu Parceiro Chamegoso',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildPartnerSection(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Verificar se está sendo usado como tab ou como tela independente
    final bool isInTabView = ModalRoute.of(context)?.settings.name == null;

    return isInTabView
      ? SafeArea(child: _buildBody(context))
      : Scaffold(
          appBar: AppBar(
            title: const Text('Perfil'),
            elevation: 0,
          ),
          body: SafeArea(child: _buildBody(context)),
        );
  }

  Widget _buildNoPartnerSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Você ainda não tem um parceiro vinculado.',
          style: TextStyle(
            fontSize: 16,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Para vincular um parceiro, compartilhe seu ID com ele para que ele possa se registrar usando seu ID, ou peça o ID dele para se vincular durante o registro.',
          style: TextStyle(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              final authProvider = Provider.of<AuthProvider>(context, listen: false);
              Clipboard.setData(ClipboardData(text: '${authProvider.authResponse?.userId ?? ""}'));
              Fluttertoast.showToast(
                msg: "ID copiado para a área de transferência",
                toastLength: Toast.LENGTH_SHORT,
                gravity: ToastGravity.BOTTOM,
                backgroundColor: Colors.green,
                textColor: Colors.white,
              );
            },
            icon: const Icon(Icons.share),
            label: const Text('COMPARTILHAR MEU ID'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF7ECBDE),
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPartnerSection() {
    // Verificar se temos um parceiro válido
    if (_partner == null) {
      return _buildNoPartnerSection();
    }

    // Obter a primeira letra do nome do parceiro para o avatar
    String firstLetter = 'P';
    if (_partner!.displayName != null && _partner!.displayName!.isNotEmpty) {
      firstLetter = _partner!.displayName![0].toUpperCase();
    } else if (_partner!.username.isNotEmpty) {
      firstLetter = _partner!.username[0].toUpperCase();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: const Color(0xFFF5F8FA),
              child: Text(
                firstLetter,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF7ECBDE),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _partner!.displayName ?? _partner!.username,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    _partner!.email,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black54,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'ID: ${_partner!.id}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        const Text(
          'Vocês estão vinculados! Agora vocês podem trocar chamegos virtuais e notificações.',
          style: TextStyle(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: TextButton.icon(
            onPressed: _showUnlinkConfirmationDialog,
            icon: const Icon(Icons.link_off, color: Colors.red),
            label: const Text(
              'DESVINCULAR PARCEIRO',
              style: TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: const BorderSide(color: Colors.red, width: 1),
              ),
            ),
          ),
        ),

        // Botão de configuração de ambiente (apenas em desenvolvimento)
        if (AppConfig.isDevelopment) ...[
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: TextButton.icon(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const EnvironmentConfigScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.settings, color: Colors.blue),
              label: const Text(
                'CONFIGURAÇÕES DE AMBIENTE',
                style: TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: const BorderSide(color: Colors.blue, width: 1),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  // Método para mostrar o diálogo de confirmação de desvinculação
  void _showUnlinkConfirmationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Desvincular Parceiro'),
        content: const Text(
          'Tem certeza que deseja desvincular seu parceiro? Esta ação afetará ambos os usuários e não poderá ser desfeita automaticamente.',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCELAR'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _unlinkPartner();
            },
            child: const Text(
              'DESVINCULAR',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  // Método para desvincular o parceiro
  Future<void> _unlinkPartner() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _userService.unlinkPartner();

      if (success) {
        Fluttertoast.showToast(
          msg: "Parceiro desvinculado com sucesso",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.green,
          textColor: Colors.white,
        );

        // Recarregar os dados
        await _loadData();
      } else {
        Fluttertoast.showToast(
          msg: "Erro ao desvincular parceiro",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );

        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: "Erro ao desvincular parceiro: $e",
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );

      setState(() {
        _isLoading = false;
      });
    }
  }
}
