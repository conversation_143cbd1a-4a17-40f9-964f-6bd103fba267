-- Script para verificar estrutura atual e migrar feedback para diário
-- Execute este script primeiro para ver a estrutura atual

-- ========================================
-- VERIFICAR ESTRUTURA ATUAL
-- ========================================
PRINT '=== ESTRUTURA ATUAL DA TABELA FEEDBACKS ===';

-- Verificar se a tabela existe
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Feedbacks')
BEGIN
    PRINT 'Tabela Feedbacks encontrada';
    
    -- <PERSON>rar colunas existentes
    SELECT 
        COLUMN_NAME as 'Coluna',
        DATA_TYPE as 'Tipo',
        IS_NULLABLE as 'Permite_NULL',
        COLUMN_DEFAULT as 'Valor_Padrao'
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'Feedbacks'
    ORDER BY ORDINAL_POSITION;
    
    -- <PERSON>tar registros
    DECLARE @TotalRecords INT;
    SELECT @TotalRecords = COUNT(*) FROM Feedbacks;
    PRINT 'Total de registros: ' + CAST(@TotalRecords AS VARCHAR(10));
    
END
ELSE
BEGIN
    PRINT 'ERRO: Tabela Feedbacks não encontrada!';
END

-- ========================================
-- VERIFICAR QUAIS COLUNAS PRECISAM SER MIGRADAS
-- ========================================
PRINT '';
PRINT '=== VERIFICAÇÃO DE MIGRAÇÃO ===';

-- Verificar se já tem FeedbackDate
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'FeedbackDate')
BEGIN
    PRINT '✓ Coluna FeedbackDate já existe';
END
ELSE
BEGIN
    PRINT '✗ Coluna FeedbackDate precisa ser criada';
END

-- Verificar se tem WeekNumber e Year (dados antigos)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'WeekNumber')
AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'Year')
BEGIN
    PRINT '✓ Colunas WeekNumber e Year encontradas (dados para migrar)';
END
ELSE
BEGIN
    PRINT '✗ Colunas WeekNumber e Year não encontradas';
END

-- Verificar HighlightOfWeek vs HighlightOfDay
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'HighlightOfWeek')
BEGIN
    PRINT '✗ Coluna HighlightOfWeek precisa ser renomeada para HighlightOfDay';
END
ELSE IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'HighlightOfDay')
BEGIN
    PRINT '✓ Coluna HighlightOfDay já existe';
END
ELSE
BEGIN
    PRINT '? Nenhuma coluna de highlight encontrada';
END

-- Verificar constraint única
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'UQ_Feedbacks_UserDate')
BEGIN
    PRINT '✓ Constraint única UQ_Feedbacks_UserDate já existe';
END
ELSE
BEGIN
    PRINT '✗ Constraint única UQ_Feedbacks_UserDate precisa ser criada';
END

PRINT '';
PRINT '=== PRÓXIMOS PASSOS ===';
PRINT 'Se você viu "✗" em algum item acima, execute o script de migração apropriado.';
PRINT 'Se todos os itens mostram "✓", a migração já foi concluída.';

-- ========================================
-- MOSTRAR DADOS DE EXEMPLO
-- ========================================
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Feedbacks')
BEGIN
    PRINT '';
    PRINT '=== DADOS DE EXEMPLO (TOP 3) ===';
    
    SELECT TOP 3 
        Id,
        UserId,
        SatisfactionRating,
        CASE 
            WHEN LEN(Comment) > 30 THEN LEFT(Comment, 30) + '...'
            ELSE Comment 
        END AS Comment_Preview,
        CreatedAt,
        -- Mostrar colunas condicionalmente
        CASE 
            WHEN EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'WeekNumber')
            THEN CAST(WeekNumber AS VARCHAR(10))
            ELSE 'N/A'
        END AS WeekNumber,
        CASE 
            WHEN EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'Year')
            THEN CAST(Year AS VARCHAR(10))
            ELSE 'N/A'
        END AS Year,
        CASE 
            WHEN EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'FeedbackDate')
            THEN CAST(FeedbackDate AS VARCHAR(20))
            ELSE 'N/A'
        END AS FeedbackDate
    FROM Feedbacks 
    ORDER BY Id DESC;
END
