using System.ComponentModel.DataAnnotations;

namespace ProjetoChamegoes.Models.DTOs;

public class CreateFeedbackDTO
{
    [Required]
    [Range(1, 10)]
    public int SatisfactionRating { get; set; }

    [Required]
    [StringLength(500)]
    public string Comment { get; set; } = string.Empty;

    [StringLength(500)]
    public string? HighlightOfDay { get; set; }

    [StringLength(500)]
    public string? ImprovementSuggestion { get; set; }

    // Data do feedback (opcional, se não informada usa a data atual)
    public DateTime? FeedbackDate { get; set; }
}

public class FeedbackDTO
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public string Username { get; set; } = string.Empty;
    public int SatisfactionRating { get; set; }
    public string Comment { get; set; } = string.Empty;
    public string? HighlightOfDay { get; set; }
    public string? ImprovementSuggestion { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime FeedbackDate { get; set; }
}
