import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';

class ApiService {
  // URL base configurada automaticamente baseada no ambiente
  static String get baseUrl => AppConfig.baseUrl;

  // Configurações de timeout
  static Duration get timeout => AppConfig.apiTimeout;

  // Método para verificar a conexão com a API
  Future<Map<String, dynamic>> checkConnection() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/utils/is-weekend'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(AppConfig.connectionTimeout);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': 'Conexão estabelecida com sucesso',
          'statusCode': response.statusCode,
          'data': response.body,
          'environment': AppConfig.currentEnvironment.name,
          'baseUrl': baseUrl,
        };
      } else {
        return {
          'success': false,
          'message': 'Servidor respondeu com erro',
          'statusCode': response.statusCode,
          'data': response.body,
          'environment': AppConfig.currentEnvironment.name,
          'baseUrl': baseUrl,
        };
      }
    } catch (e) {
      // Log detalhado apenas em desenvolvimento
      if (AppConfig.enableDebugLogs) {
        // ignore: avoid_print
        print('Erro ao conectar com a API: $e');
        // ignore: avoid_print
        print('URL tentativa: $baseUrl/utils/is-weekend');
        // ignore: avoid_print
        print('Ambiente: ${AppConfig.currentEnvironment.name}');
      }

      return {
        'success': false,
        'message': 'Falha ao conectar com o servidor',
        'error': e.toString(),
        'environment': AppConfig.currentEnvironment.name,
        'baseUrl': baseUrl,
      };
    }
  }

  // Método para obter o token JWT armazenado
  Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token');
  }

  // Método para salvar o token JWT
  Future<void> saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('token', token);
  }

  // Método para limpar o token JWT (logout)
  Future<void> clearToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('token');
  }

  // Método para criar cabeçalhos com autenticação
  Future<Map<String, String>> _getHeaders({bool requireAuth = true}) async {
    Map<String, String> headers = {
      'Content-Type': 'application/json',
    };

    if (requireAuth) {
      final token = await getToken();
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }
    }

    return headers;
  }

  // Método genérico para fazer requisições GET
  Future<dynamic> get(String endpoint, {bool requireAuth = true}) async {
    final headers = await _getHeaders(requireAuth: requireAuth);
    final response = await http.get(
      Uri.parse('$baseUrl/$endpoint'),
      headers: headers,
    ).timeout(timeout);

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      final errorMessage = AppConfig.enableDetailedErrors
          ? 'Falha ao carregar dados: ${response.statusCode} - ${response.body}'
          : 'Falha ao carregar dados: ${response.statusCode}';
      throw Exception(errorMessage);
    }
  }

  // Método genérico para fazer requisições POST
  Future<dynamic> post(String endpoint, dynamic data, {bool requireAuth = true}) async {
    final headers = await _getHeaders(requireAuth: requireAuth);
    final response = await http.post(
      Uri.parse('$baseUrl/$endpoint'),
      headers: headers,
      body: json.encode(data),
    ).timeout(timeout);

    if (response.statusCode == 200 || response.statusCode == 201) {
      return json.decode(response.body);
    } else {
      final errorMessage = AppConfig.enableDetailedErrors
          ? 'Falha ao enviar dados: ${response.statusCode} - ${response.body}'
          : 'Falha ao enviar dados: ${response.statusCode}';
      throw Exception(errorMessage);
    }
  }

  // Método genérico para fazer requisições PUT
  Future<dynamic> put(String endpoint, dynamic data, {bool requireAuth = true}) async {
    final headers = await _getHeaders(requireAuth: requireAuth);
    final response = await http.put(
      Uri.parse('$baseUrl/$endpoint'),
      headers: headers,
      body: json.encode(data),
    ).timeout(timeout);

    if (response.statusCode == 200 || response.statusCode == 204) {
      if (response.body.isNotEmpty) {
        return json.decode(response.body);
      }
      return null;
    } else {
      final errorMessage = AppConfig.enableDetailedErrors
          ? 'Falha ao atualizar dados: ${response.statusCode} - ${response.body}'
          : 'Falha ao atualizar dados: ${response.statusCode}';
      throw Exception(errorMessage);
    }
  }

  // Método genérico para fazer requisições DELETE
  Future<dynamic> delete(String endpoint, {bool requireAuth = true}) async {
    final headers = await _getHeaders(requireAuth: requireAuth);
    final response = await http.delete(
      Uri.parse('$baseUrl/$endpoint'),
      headers: headers,
    ).timeout(timeout);

    if (response.statusCode == 200 || response.statusCode == 204) {
      if (response.body.isNotEmpty) {
        return json.decode(response.body);
      }
      return null;
    } else {
      final errorMessage = AppConfig.enableDetailedErrors
          ? 'Falha ao excluir dados: ${response.statusCode} - ${response.body}'
          : 'Falha ao excluir dados: ${response.statusCode}';
      throw Exception(errorMessage);
    }
  }
}
