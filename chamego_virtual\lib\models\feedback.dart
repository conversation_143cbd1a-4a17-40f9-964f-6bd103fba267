class Feedback {
  final int? id;
  final int userId;
  final String username;
  final int satisfactionRating;
  final String comment;
  final String? highlightOfDay;
  final String? improvementSuggestion;
  final DateTime createdAt;
  final DateTime feedbackDate;

  Feedback({
    this.id,
    required this.userId,
    required this.username,
    required this.satisfactionRating,
    required this.comment,
    this.highlightOfDay,
    this.improvementSuggestion,
    required this.createdAt,
    required this.feedbackDate,
  });

  factory Feedback.fromJson(Map<String, dynamic> json) {
    return Feedback(
      id: json['id'],
      userId: json['userId'],
      username: json['username'],
      satisfactionRating: json['satisfactionRating'],
      comment: json['comment'],
      highlightOfDay: json['highlightOfDay'],
      improvementSuggestion: json['improvementSuggestion'],
      createdAt: DateTime.parse(json['createdAt']),
      feedbackDate: DateTime.parse(json['feedbackDate']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'satisfactionRating': satisfactionRating,
      'comment': comment,
      'highlightOfDay': highlightOfDay,
      'improvementSuggestion': improvementSuggestion,
      'feedbackDate': feedbackDate.toIso8601String(),
    };
  }
}
