import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:chamego_virtual/models/feedback.dart' as app_feedback;
import 'package:chamego_virtual/services/feedback_service.dart';
import 'package:chamego_virtual/services/utils_service.dart';

class FeedbackScreen extends StatefulWidget {
  const FeedbackScreen({super.key});

  @override
  State<FeedbackScreen> createState() => _FeedbackScreenState();
}

class _FeedbackScreenState extends State<FeedbackScreen> with SingleTickerProviderStateMixin {
  final FeedbackService _feedbackService = FeedbackService();
  final UtilsService _utilsService = UtilsService();
  final _formKey = GlobalKey<FormState>();
  final _commentController = TextEditingController();
  final _highlightController = TextEditingController();
  final _improvementController = TextEditingController();
  int _satisfactionRating = 7;

  late TabController _tabController;
  List<app_feedback.Feedback> _feedbacks = [];
  app_feedback.Feedback? _latestFeedback;
  bool _isLoading = true;
  bool _isSubmitting = false;
  bool _isFeedbackAvailable = false;
  String _feedbackAvailabilityMessage = "";

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _commentController.dispose();
    _highlightController.dispose();
    _improvementController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Verificar se já existe feedback para hoje
      final todayFeedback = await _feedbackService.getTodayFeedback();

      // Carregar feedbacks existentes
      final feedbacks = await _feedbackService.getFeedbacks();
      final latestFeedback = await _feedbackService.getLatestFeedback();

      setState(() {
        _isFeedbackAvailable = todayFeedback == null; // Disponível se não há feedback hoje
        _feedbackAvailabilityMessage = todayFeedback != null
            ? 'Você já enviou feedback hoje. Tente novamente amanhã.'
            : 'Feedback disponível para hoje';
        _feedbacks = feedbacks;
        _latestFeedback = latestFeedback;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      Fluttertoast.showToast(
        msg: "Erro ao carregar dados: $e",
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  Future<void> _submitFeedback() async {
    if (!_isFeedbackAvailable) {
      Fluttertoast.showToast(
        msg: _feedbackAvailabilityMessage,
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.orange,
        textColor: Colors.white,
      );
      return;
    }

    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSubmitting = true;
      });

      try {
        // Verificar novamente a disponibilidade do feedback (para garantir que ainda está disponível)
        final availability = await _utilsService.checkFeedbackAvailability();
        if (!(availability['isAvailable'] as bool)) {
          Fluttertoast.showToast(
            msg: availability['message'] as String,
            toastLength: Toast.LENGTH_LONG,
            gravity: ToastGravity.BOTTOM,
            backgroundColor: Colors.orange,
            textColor: Colors.white,
          );
          setState(() {
            _isSubmitting = false;
          });
          return;
        }

        await _feedbackService.createFeedback(
          _satisfactionRating,
          _commentController.text,
          _highlightController.text.isEmpty ? null : _highlightController.text,
          _improvementController.text.isEmpty ? null : _improvementController.text,
          feedbackDate: DateTime.now(), // Data atual para feedback diário
        );

        // Limpar o formulário
        _commentController.clear();
        _highlightController.clear();
        _improvementController.clear();
        setState(() {
          _satisfactionRating = 7;
        });

        // Recarregar dados
        await _loadData();

        Fluttertoast.showToast(
          msg: "Feedback enviado com sucesso!",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.green,
          textColor: Colors.white,
        );
      } catch (e) {
        Fluttertoast.showToast(
          msg: "Erro ao enviar feedback: $e",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
      } finally {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Feedback Diário'),
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: const Color(0xFF7ECBDE),
          indicatorWeight: 3,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
          tabs: const [
            Tab(text: 'Novo Feedback'),
            Tab(text: 'Histórico'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildFeedbackForm(),
          _buildFeedbackHistory(),
        ],
      ),
    );
  }

  Widget _buildFeedbackForm() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    // Exibir mensagem quando o feedback não estiver disponível
    if (!_isFeedbackAvailable) {
      return SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Feedback Diário',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Feedback não disponível',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _feedbackAvailabilityMessage,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Compartilhe seu feedback diário',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Nível de satisfação:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            Slider(
              value: _satisfactionRating.toDouble(),
              min: 1,
              max: 10,
              divisions: 9,
              label: _satisfactionRating.toString(),
              onChanged: (value) {
                setState(() {
                  _satisfactionRating = value.toInt();
                });
              },
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: const [
                Text('1'),
                Text('5'),
                Text('10'),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _commentController,
              decoration: const InputDecoration(
                labelText: 'Comentário geral',
                hintText: 'Como foi seu dia?',
                filled: true,
                fillColor: Color(0xFFF5F8FA),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                  borderSide: BorderSide(color: Color(0xFF7ECBDE), width: 1),
                ),
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Por favor, insira um comentário';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _highlightController,
              decoration: const InputDecoration(
                labelText: 'Destaque do dia (opcional)',
                hintText: 'O que foi mais especial?',
                filled: true,
                fillColor: Color(0xFFF5F8FA),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                  borderSide: BorderSide(color: Color(0xFF7ECBDE), width: 1),
                ),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _improvementController,
              decoration: const InputDecoration(
                labelText: 'Sugestão de melhoria (opcional)',
                hintText: 'O que poderia melhorar?',
                filled: true,
                fillColor: Color(0xFFF5F8FA),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                  borderSide: BorderSide(color: Color(0xFF7ECBDE), width: 1),
                ),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _isSubmitting ? null : _submitFeedback,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF7ECBDE),
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
                child: _isSubmitting
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Text(
                        'ENVIAR FEEDBACK',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 1.0,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeedbackHistory() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_latestFeedback != null)
              Card(
                elevation: 4,
                color: const Color(0xFFE1F5F9),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Feedback mais recente',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text('Satisfação: ${_latestFeedback!.satisfactionRating}/10'),
                      Text('Data: ${DateFormat('dd/MM/yyyy').format(_latestFeedback!.createdAt)}'),
                      const SizedBox(height: 8),
                      Text('Comentário: ${_latestFeedback!.comment}'),
                      if (_latestFeedback!.highlightOfDay != null)
                        Text('Destaque: ${_latestFeedback!.highlightOfDay}'),
                      if (_latestFeedback!.improvementSuggestion != null)
                        Text('Sugestão: ${_latestFeedback!.improvementSuggestion}'),
                    ],
                  ),
                ),
              ),
            const SizedBox(height: 16),
            const Text(
              'Histórico de Feedbacks',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            _feedbacks.isEmpty
                ? const Center(
                    child: Text('Nenhum feedback registrado ainda.'),
                  )
                : ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _feedbacks.length,
                    itemBuilder: (context, index) {
                      final feedback = _feedbacks[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ExpansionTile(
                          title: Text('${DateFormat('dd/MM/yyyy').format(feedback.feedbackDate)}'),
                          subtitle: Text('Satisfação: ${feedback.satisfactionRating}/10'),
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Comentário: ${feedback.comment}'),
                                  if (feedback.highlightOfDay != null)
                                    Text('Destaque: ${feedback.highlightOfDay}'),
                                  if (feedback.improvementSuggestion != null)
                                    Text('Sugestão: ${feedback.improvementSuggestion}'),
                                  Text('Data: ${DateFormat('dd/MM/yyyy').format(feedback.createdAt)}'),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
          ],
        ),
      ),
    );
  }
}
