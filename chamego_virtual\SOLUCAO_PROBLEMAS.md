# Solução de Problemas - Chamego Virtual

## Problema: Connection Refused (Conexão Recusada)

### Sintomas:
- <PERSON><PERSON>: `ClientException with SocketException: Connection refused`
- App não consegue se conectar ao servidor
- Mensagens de timeout ou falha de conexão

### Soluções:

#### 1. Verificar se o servidor está rodando
```bash
# No diretório do projeto backend
dotnet run --urls="http://0.0.0.0:5032"
```

#### 2. Verificar a porta correta
- O servidor deve estar rodando na porta **5032**
- Verifique se não há outro processo usando esta porta

#### 3. Verificar o IP da máquina (para dispositivos físicos)
```bash
# Windows
ipconfig

# Procure por "Adaptador de Rede sem Fio Wi-Fi"
# Anote o "Endereço IPv4" (ex: *************)
```

#### 4. Atualizar configuração do app
Edite `lib/config/app_config.dart`:
```dart
// Para emulador Android
Environment.development → http://********:5032/api

// Para dispositivo físico (substitua pelo seu IP)
Environment.developmentPhysical → http://SEU_IP_AQUI:5032/api
```

#### 5. Verificar firewall
- Windows: Permita o app através do Windows Defender
- Antivírus: Adicione exceção para a porta 5032

## Problema: Overflow de UI

### Sintomas:
- Texto cortado na interface
- Elementos visuais sobrepostos
- Mensagem de overflow no console

### Solução Aplicada:
✅ Corrigido o texto "Tem um chamegoso?" na tela de registro
- Adicionado `Expanded` widget
- Configurado `overflow: TextOverflow.ellipsis`
- Permitido até 2 linhas de texto

## Como Testar a Conexão

### 1. Usando a tela de configurações do app:
1. Abra o app
2. Vá para Perfil
3. Toque em "CONFIGURAÇÕES DE AMBIENTE"
4. Toque em "Testar Conexão"

### 2. Testando manualmente no navegador:
```
http://SEU_IP:5032/api/utils/is-weekend
```

### 3. Verificando se o servidor responde:
```bash
# Windows
telnet SEU_IP 5032

# Se conectar, o servidor está rodando
```

## Configurações por Tipo de Dispositivo

### Emulador Android:
- IP: `********:5032`
- Ambiente: `Environment.development`

### Dispositivo Android Físico:
- IP: `SEU_IP_LOCAL:5032` (ex: *************:5032)
- Ambiente: `Environment.developmentPhysical`
- **Importante**: Dispositivo deve estar na mesma rede WiFi

### iOS Simulator:
- IP: `localhost:5032`
- Ambiente: `Environment.developmentWeb`

### Dispositivo iOS Físico:
- IP: `SEU_IP_LOCAL:5032`
- Ambiente: `Environment.developmentPhysical`

## Checklist de Verificação

### Servidor:
- [ ] Servidor está rodando na porta 5032
- [ ] Comando usado: `dotnet run --urls="http://0.0.0.0:5032"`
- [ ] Swagger acessível em: `http://localhost:5032/swagger`

### Rede:
- [ ] Dispositivo na mesma rede WiFi (se físico)
- [ ] Firewall não está bloqueando a porta 5032
- [ ] IP da máquina está correto no app

### App:
- [ ] Ambiente correto configurado em `app_config.dart`
- [ ] App reiniciado após mudança de configuração
- [ ] Permissões de internet configuradas

## Logs Úteis

### No app (console do Flutter):
```
flutter logs
```

### No servidor:
```
dotnet run --verbosity detailed
```

## Contato para Suporte

Se os problemas persistirem:
1. Anote o erro exato exibido
2. Verifique qual ambiente está configurado
3. Confirme se o servidor está rodando
4. Teste a conexão usando a tela de configurações do app
