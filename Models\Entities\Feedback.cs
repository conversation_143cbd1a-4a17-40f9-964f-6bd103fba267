using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ProjetoChamegoes.Models.Entities;

public class Feedback
{
    [Key]
    public int Id { get; set; }
    
    [Required]
    public int UserId { get; set; }
    
    [ForeignKey("UserId")]
    public virtual User User { get; set; } = null!;
    
    [Required]
    [Range(1, 10)]
    public int SatisfactionRating { get; set; }
    
    [Required]
    [StringLength(500)]
    public string Comment { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string? HighlightOfDay { get; set; }

    [StringLength(500)]
    public string? ImprovementSuggestion { get; set; }

    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Data específica do feedback (apenas a data, sem horário)
    [Required]
    [Column(TypeName = "date")]
    public DateTime FeedbackDate { get; set; }
}
