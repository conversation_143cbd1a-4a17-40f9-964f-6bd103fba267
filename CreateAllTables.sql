-- Script para criar todas as tabelas do projeto Chamego Virtual
-- Criado para implementação em SQL Server

-- NOTA: Este script apenas define as tabelas e índices
-- Não cria nem seleciona o banco de dados
-- Execute este script no contexto do banco de dados já existente

-- Tabela de Usuários
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Users')
BEGIN
    CREATE TABLE Users (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        Username NVARCHAR(50) NOT NULL,
        Email NVARCHAR(100) NOT NULL,
        PasswordHash VARBINARY(MAX) NOT NULL,
        PasswordSalt VARBINARY(MAX) NOT NULL,
        DisplayName NVARCHAR(100) NULL,
        PartnerId INT NULL,
        CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
        LastLogin DATETIME NULL,
        FcmToken NVARCHAR(500) NULL,
        CONSTRAINT UQ_Users_Username UNIQUE (Username),
        CONSTRAINT UQ_Users_Email UNIQUE (Email)
    );
END
GO

-- Adicionar chave estrangeira para auto-referência após a criação da tabela
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Users')
AND NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Users_Partner')
BEGIN
    ALTER TABLE Users
    ADD CONSTRAINT FK_Users_Partner FOREIGN KEY (PartnerId) REFERENCES Users(Id);
END
GO

-- Tabela de Feedbacks
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Feedbacks')
BEGIN
    CREATE TABLE Feedbacks (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        UserId INT NOT NULL,
        SatisfactionRating INT NOT NULL,
        Comment NVARCHAR(500) NOT NULL,
        HighlightOfWeek NVARCHAR(500) NULL,
        ImprovementSuggestion NVARCHAR(500) NULL,
        CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
        WeekNumber INT NOT NULL,
        Year INT NOT NULL,
        CONSTRAINT FK_Feedbacks_Users FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE
    );
END
GO

-- Tabela de Chamegos
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Chamegos')
BEGIN
    CREATE TABLE Chamegos (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        UserId INT NOT NULL,
        Title NVARCHAR(100) NOT NULL,
        Description NVARCHAR(500) NULL,
        CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
        IsWeekend BIT NOT NULL,
        Type NVARCHAR(50) NULL,
        IntensityLevel INT NULL,
        CONSTRAINT FK_Chamegos_Users FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE
    );
END
GO

-- Tabela de Chamegos Virtuais
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'VirtualChamegos')
BEGIN
    CREATE TABLE VirtualChamegos (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        SenderId INT NOT NULL,
        ReceiverId INT NOT NULL,
        Message NVARCHAR(200) NULL,
        Type NVARCHAR(50) NOT NULL,
        MediaUrl NVARCHAR(500) NULL,
        SentAt DATETIME NOT NULL DEFAULT GETDATE(),
        ReadAt DATETIME NULL,
        IsRead BIT NOT NULL DEFAULT 0,
        CONSTRAINT FK_VirtualChamegos_Sender FOREIGN KEY (SenderId) REFERENCES Users(Id),
        CONSTRAINT FK_VirtualChamegos_Receiver FOREIGN KEY (ReceiverId) REFERENCES Users(Id)
    );
END
GO

-- Tabela de Mensagens da Cápsula
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'CapsuleMessages')
BEGIN
    CREATE TABLE CapsuleMessages (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        SenderId INT NOT NULL,
        ReceiverId INT NOT NULL,
        Title NVARCHAR(100) NOT NULL,
        Content NVARCHAR(1000) NOT NULL,
        ContentType NVARCHAR(50) NOT NULL,
        MediaUrl NVARCHAR(500) NULL,
        CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
        ScheduledFor DATETIME NOT NULL,
        DeliveredAt DATETIME NULL,
        ReadAt DATETIME NULL,
        IsDelivered BIT NOT NULL DEFAULT 0,
        IsRead BIT NOT NULL DEFAULT 0,
        CONSTRAINT FK_CapsuleMessages_Sender FOREIGN KEY (SenderId) REFERENCES Users(Id),
        CONSTRAINT FK_CapsuleMessages_Receiver FOREIGN KEY (ReceiverId) REFERENCES Users(Id)
    );
END
GO

-- Tabela de Registros de Humor (Mood)
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'MoodEntries')
BEGIN
    CREATE TABLE MoodEntries (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        UserId INT NOT NULL,
        MoodDate DATE NOT NULL,
        MoodValue INT NOT NULL CHECK (MoodValue BETWEEN 0 AND 5),
        Note NVARCHAR(200) NULL,
        CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
        CONSTRAINT FK_MoodEntries_Users FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE,
        CONSTRAINT UQ_MoodEntries_UserDate UNIQUE (UserId, MoodDate)
    );
END
GO

-- Tabela de Contagem Semanal de Chamegos Virtuais
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'WeeklyVirtualChamegoCount')
BEGIN
    CREATE TABLE WeeklyVirtualChamegoCount (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        UserId INT NOT NULL,
        PartnerId INT NOT NULL,
        WeekNumber INT NOT NULL,
        Year INT NOT NULL,
        SentCount INT NOT NULL DEFAULT 0,
        ReceivedCount INT NOT NULL DEFAULT 0,
        LastUpdated DATETIME NOT NULL DEFAULT GETDATE(),
        CONSTRAINT FK_WeeklyVirtualChamegoCount_User FOREIGN KEY (UserId) REFERENCES Users(Id),
        CONSTRAINT FK_WeeklyVirtualChamegoCount_Partner FOREIGN KEY (PartnerId) REFERENCES Users(Id),
        CONSTRAINT UQ_WeeklyVirtualChamegoCount_User_Week UNIQUE (UserId, WeekNumber, Year)
    );
END
GO

-- Criação de índices para otimização de consultas
-- Índices para Feedbacks
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Feedbacks_UserId')
BEGIN
    CREATE INDEX IX_Feedbacks_UserId ON Feedbacks(UserId);
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Feedbacks_WeekNumber_Year')
BEGIN
    CREATE INDEX IX_Feedbacks_WeekNumber_Year ON Feedbacks(WeekNumber, Year);
END
GO

-- Índices para Chamegos
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Chamegos_UserId')
BEGIN
    CREATE INDEX IX_Chamegos_UserId ON Chamegos(UserId);
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Chamegos_IsWeekend')
BEGIN
    CREATE INDEX IX_Chamegos_IsWeekend ON Chamegos(IsWeekend);
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Chamegos_CreatedAt')
BEGIN
    CREATE INDEX IX_Chamegos_CreatedAt ON Chamegos(CreatedAt);
END
GO

-- Índices para VirtualChamegos
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_VirtualChamegos_SenderId')
BEGIN
    CREATE INDEX IX_VirtualChamegos_SenderId ON VirtualChamegos(SenderId);
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_VirtualChamegos_ReceiverId')
BEGIN
    CREATE INDEX IX_VirtualChamegos_ReceiverId ON VirtualChamegos(ReceiverId);
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_VirtualChamegos_SentAt')
BEGIN
    CREATE INDEX IX_VirtualChamegos_SentAt ON VirtualChamegos(SentAt);
END
GO

-- Índices para CapsuleMessages
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CapsuleMessages_SenderId')
BEGIN
    CREATE INDEX IX_CapsuleMessages_SenderId ON CapsuleMessages(SenderId);
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CapsuleMessages_ReceiverId')
BEGIN
    CREATE INDEX IX_CapsuleMessages_ReceiverId ON CapsuleMessages(ReceiverId);
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CapsuleMessages_ScheduledFor')
BEGIN
    CREATE INDEX IX_CapsuleMessages_ScheduledFor ON CapsuleMessages(ScheduledFor);
END
GO

-- Índices para MoodEntries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MoodEntries_UserId')
BEGIN
    CREATE INDEX IX_MoodEntries_UserId ON MoodEntries(UserId);
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MoodEntries_MoodDate')
BEGIN
    CREATE INDEX IX_MoodEntries_MoodDate ON MoodEntries(MoodDate);
END
GO

-- Índices para WeeklyVirtualChamegoCount
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_WeeklyVirtualChamegoCount_UserId')
BEGIN
    CREATE INDEX IX_WeeklyVirtualChamegoCount_UserId ON WeeklyVirtualChamegoCount(UserId);
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_WeeklyVirtualChamegoCount_PartnerId')
BEGIN
    CREATE INDEX IX_WeeklyVirtualChamegoCount_PartnerId ON WeeklyVirtualChamegoCount(PartnerId);
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_WeeklyVirtualChamegoCount_WeekNumber_Year')
BEGIN
    CREATE INDEX IX_WeeklyVirtualChamegoCount_WeekNumber_Year ON WeeklyVirtualChamegoCount(WeekNumber, Year);
END
GO

-- Não exibe mensagens para não interferir com os logs da aplicação
-- PRINT 'Todas as tabelas e índices foram criados com sucesso!';
GO
