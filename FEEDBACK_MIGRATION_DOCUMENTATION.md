# Documentação: Migração de Feedback Semanal para Diário

## Resumo das Alterações

O sistema de feedback foi migrado de **semanal** para **diário**, permitindo que os usuários enviem feedback todos os dias ao invés de apenas uma vez por semana.

## Alterações no Backend (ASP.NET Core)

### 1. Entidade Feedback (`Models/Entities/Feedback.cs`)
**Alterações:**
- ✅ Removido: `WeekNumber` e `Year`
- ✅ Adicionado: `FeedbackDate` (DateTime, apenas data)
- ✅ Renomeado: `HighlightOfWeek` → `HighlightOfDay`

**Antes:**
```csharp
public int WeekNumber { get; set; }
public int Year { get; set; }
public string? HighlightOfWeek { get; set; }
```

**Depois:**
```csharp
public DateTime FeedbackDate { get; set; }
public string? HighlightOfDay { get; set; }
```

### 2. DTOs (`Models/DTOs/FeedbackDTO.cs`)
**Alterações:**
- ✅ `CreateFeedbackDTO`: Adicionado `FeedbackDate` opcional
- ✅ `FeedbackDTO`: Substituído `WeekNumber/Year` por `FeedbackDate`
- ✅ Renomeado: `HighlightOfWeek` → `HighlightOfDay`

### 3. Repository (`Data/Repositories/FeedbackRepository.cs`)
**Novos métodos:**
- ✅ `ExistsForDateAsync(int userId, DateTime feedbackDate)`
- ✅ `GetByUserIdAndDateAsync(int userId, DateTime feedbackDate)`
- ✅ `GetByUserIdAndDateRangeAsync(int userId, DateTime startDate, DateTime endDate)`

**Métodos atualizados:**
- ✅ `GetByUserIdAsync()` - Ordenação por `FeedbackDate DESC`
- ✅ `GetLatestByUserIdAsync()` - Ordenação por `FeedbackDate DESC`
- ✅ `CreateAsync()` - Usa `FeedbackDate` e `HighlightOfDay`
- ✅ `UpdateAsync()` - Usa `HighlightOfDay`

### 4. Controller (`Controllers/FeedbackController.cs`)
**Alterações:**
- ✅ Removida verificação de disponibilidade semanal
- ✅ Adicionada verificação de feedback diário único
- ✅ Novos endpoints:
  - `GET /api/feedback/today` - Feedback de hoje
  - `GET /api/feedback/date/{date}` - Feedback de data específica
  - `GET /api/feedback/range` - Feedbacks por período

**Lógica atualizada:**
- ✅ Permite apenas um feedback por dia por usuário
- ✅ Data do feedback configurável (padrão: data atual)

## Alterações no Frontend (Flutter)

### 1. Modelo (`lib/models/feedback.dart`)
**Alterações:**
- ✅ Removido: `weekNumber` e `year`
- ✅ Adicionado: `feedbackDate`
- ✅ Renomeado: `highlightOfWeek` → `highlightOfDay`

### 2. Serviço (`lib/services/feedback_service.dart`)
**Novos métodos:**
- ✅ `getTodayFeedback()` - Feedback de hoje
- ✅ `getFeedbackByDate(DateTime date)` - Feedback de data específica
- ✅ `getFeedbacksByDateRange()` - Feedbacks por período

**Método atualizado:**
- ✅ `createFeedback()` - Aceita `feedbackDate` opcional

### 3. Tela de Feedback (`lib/screens/feedback_screen.dart`)
**Alterações de Interface:**
- ✅ "Feedback Semanal" → "Feedback Diário"
- ✅ "Como foi sua semana?" → "Como foi seu dia?"
- ✅ "Destaque da semana" → "Destaque do dia"
- ✅ Exibição: "Semana X de YYYY" → "DD/MM/YYYY"

**Lógica atualizada:**
- ✅ Verificação de feedback existente para hoje
- ✅ Mensagem: "Você já enviou feedback hoje"

## Alterações no Banco de Dados

### 1. Script de Migração (`Data/Scripts/MigrateFeedbackToDaily.sql`)
**Funcionalidades:**
- ✅ Backup automático da tabela original
- ✅ Adição da coluna `FeedbackDate`
- ✅ Migração de dados: `WeekNumber/Year` → `FeedbackDate`
- ✅ Renomeação: `HighlightOfWeek` → `HighlightOfDay`
- ✅ Constraint única: `UserId + FeedbackDate`
- ✅ Novos índices otimizados
- ✅ Verificação da migração

### 2. Script Principal (`CreateAllTables.sql`)
**Alterações:**
- ✅ Estrutura atualizada da tabela `Feedbacks`
- ✅ Constraint única para feedback diário
- ✅ Índices otimizados para consultas por data

## Compatibilidade e Migração

### Dados Existentes
- ✅ **Preservados**: Todos os feedbacks existentes são mantidos
- ✅ **Migrados**: `WeekNumber/Year` convertidos para `FeedbackDate`
- ✅ **Backup**: Tabela original salva como `Feedbacks_Backup`

### Estratégia de Migração
1. **Backup automático** da tabela original
2. **Conversão de dados** sem perda de informações
3. **Validação** da migração com relatórios
4. **Rollback possível** através do backup

## Novos Recursos

### Backend
- ✅ Endpoint para feedback de hoje
- ✅ Endpoint para feedback por data específica
- ✅ Endpoint para feedbacks por período
- ✅ Validação de feedback único por dia

### Frontend
- ✅ Interface adaptada para feedback diário
- ✅ Verificação automática de feedback existente
- ✅ Mensagens contextuais para feedback diário

## Testes Recomendados

### Backend
- [ ] Criar feedback para hoje
- [ ] Tentar criar segundo feedback para hoje (deve falhar)
- [ ] Buscar feedback de hoje
- [ ] Buscar feedbacks por período
- [ ] Verificar migração de dados antigos

### Frontend
- [ ] Enviar feedback diário
- [ ] Verificar mensagem quando já existe feedback
- [ ] Visualizar histórico de feedbacks
- [ ] Verificar exibição de datas

### Banco de Dados
- [ ] Executar script de migração
- [ ] Verificar integridade dos dados
- [ ] Testar constraints e índices
- [ ] Validar backup

## Rollback (Se Necessário)

Em caso de problemas, é possível reverter:

```sql
-- 1. Restaurar tabela original
DROP TABLE Feedbacks;
SELECT * INTO Feedbacks FROM Feedbacks_Backup;

-- 2. Recriar índices originais
CREATE INDEX IX_Feedbacks_WeekNumber_Year ON Feedbacks(WeekNumber, Year);
```

## Conclusão

A migração foi implementada de forma segura e completa, mantendo compatibilidade com dados existentes e adicionando novos recursos para feedback diário. Todos os componentes (backend, frontend e banco de dados) foram atualizados consistentemente.
