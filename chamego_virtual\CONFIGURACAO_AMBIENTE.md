# Configuração de Ambiente - Chamego Virtual

Este documento explica como configurar o aplicativo Flutter para diferentes ambientes de desenvolvimento e produção.

## Ambientes Disponíveis

### 1. Development (Emulador Android)
- **URL**: `http://********:5032/api`
- **Uso**: Para desenvolvimento usando emulador Android
- **Descrição**: O endereço `********` é um IP especial que o emulador Android usa para acessar o localhost da máquina host

### 2. Development Physical (Dispositivo Físico)
- **URL**: `http://***************:5032/api`
- **Uso**: Para desenvolvimento usando dispositivo físico conectado na mesma rede WiFi
- **Descrição**: Substitua `***************` pelo IP real da sua máquina na rede local

### 3. Development Web (Web/iOS Simulator)
- **URL**: `http://localhost:5032/api`
- **Uso**: Para desenvolvimento usando Flutter Web ou iOS Simulator
- **Descrição**: Acesso direto ao localhost

### 4. Production
- **URL**: `http://*************/api`
- **Uso**: Para produção
- **Descrição**: Servidor de produção

## Como Trocar o Ambiente

### Método 1: Editando o arquivo de configuração

1. Abra o arquivo `lib/config/app_config.dart`
2. Localize a linha:
   ```dart
   static const Environment currentEnvironment = Environment.development;
   ```
3. Altere para o ambiente desejado:
   - `Environment.development` - Emulador Android
   - `Environment.developmentPhysical` - Dispositivo físico
   - `Environment.developmentWeb` - Web/iOS Simulator
   - `Environment.production` - Produção

4. Salve o arquivo e reinicie o aplicativo

### Método 2: Usando a tela de configuração (apenas em desenvolvimento)

1. No aplicativo, vá para a tela de **Perfil**
2. Se estiver em modo de desenvolvimento, você verá o botão **"CONFIGURAÇÕES DE AMBIENTE"**
3. Toque no botão para acessar a tela de configuração
4. Visualize as informações do ambiente atual e teste a conexão

## Configuração do Servidor de Desenvolvimento

Certifique-se de que o servidor ASP.NET Core esteja rodando na porta 5032:

```bash
dotnet run --urls="http://0.0.0.0:5032"
```

Ou use o perfil de desenvolvimento configurado em `Properties/launchSettings.json`.

## Descobrindo o IP da Sua Máquina

### Windows
```cmd
ipconfig
```
Procure pelo adaptador de rede WiFi e anote o "Endereço IPv4".

### macOS/Linux
```bash
ifconfig
```
Procure pela interface de rede ativa (geralmente `en0` no macOS ou `wlan0` no Linux).

## Testando a Conexão

1. Acesse a tela de **Configurações de Ambiente** no app
2. Toque em **"Testar Conexão"**
3. Verifique se a conexão foi bem-sucedida
4. Se houver erro, verifique:
   - Se o servidor está rodando
   - Se o IP está correto
   - Se não há firewall bloqueando a conexão
   - Se o dispositivo está na mesma rede (para dispositivos físicos)

## Configurações Específicas por Plataforma

### Android
- **Emulador**: Use `********:5032`
- **Dispositivo físico**: Use o IP da máquina na rede local
- Permissão de internet já configurada no `AndroidManifest.xml`

### iOS
- **Simulator**: Use `localhost:5032`
- **Dispositivo físico**: Use o IP da máquina na rede local

### Web
- Use `localhost:5032`
- Certifique-se de que não há problemas de CORS

## Troubleshooting

### Erro de conexão no emulador Android
- Verifique se está usando `********` e não `localhost`
- Certifique-se de que o servidor está rodando na porta 5032

### Erro de conexão em dispositivo físico
- Verifique se o dispositivo está na mesma rede WiFi
- Confirme o IP da máquina com `ipconfig` (Windows) ou `ifconfig` (macOS/Linux)
- Verifique se o firewall não está bloqueando a porta 5032

### Erro de timeout
- Aumente o timeout em `app_config.dart` se necessário
- Verifique a estabilidade da conexão de rede

## Logs de Debug

Em modo de desenvolvimento, logs detalhados são exibidos no console para ajudar na depuração de problemas de conexão.

## Produção

⚠️ **IMPORTANTE**: Antes de fazer deploy para produção, certifique-se de que:
1. O ambiente está configurado como `Environment.production`
2. A URL de produção está correta
3. Logs de debug estão desabilitados
4. Todas as funcionalidades foram testadas
