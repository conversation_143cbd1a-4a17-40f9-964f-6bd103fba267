com.example.chamego_virtual.app-preference-1.2.1-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ae860f2fb54c530241cb1bce335a021\transformed\preference-1.2.1\res
com.example.chamego_virtual.app-lifecycle-livedata-2.7.0-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e5cb85a0ef0f386454cbb8d1bbe0342\transformed\lifecycle-livedata-2.7.0\res
com.example.chamego_virtual.app-jetified-savedstate-1.2.1-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11a2063378868f4fc33a9598dc7a5622\transformed\jetified-savedstate-1.2.1\res
com.example.chamego_virtual.app-transition-1.4.1-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e2cfbac636f1391456b2c1c113bc0f5\transformed\transition-1.4.1\res
com.example.chamego_virtual.app-jetified-play-services-base-18.5.0-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21c8db8decf765cf7dd74ee06345bde5\transformed\jetified-play-services-base-18.5.0\res
com.example.chamego_virtual.app-appcompat-1.1.0-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2671cc70069c5b73b4048b0546895da7\transformed\appcompat-1.1.0\res
com.example.chamego_virtual.app-jetified-lifecycle-livedata-core-ktx-2.7.0-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f8201b8261221f84728e41c21e1b687\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.chamego_virtual.app-core-1.13.1-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34ae8682f85f5cb1e3f80c2df56ac56d\transformed\core-1.13.1\res
com.example.chamego_virtual.app-localbroadcastmanager-1.1.0-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\371ab503989961083ccd2c42bf97a21a\transformed\localbroadcastmanager-1.1.0\res
com.example.chamego_virtual.app-jetified-startup-runtime-1.1.1-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37eb3ce709de871c116556c3480e16da\transformed\jetified-startup-runtime-1.1.1\res
com.example.chamego_virtual.app-recyclerview-1.0.0-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a88cb317433eaf5f5dda36921e1a2e2\transformed\recyclerview-1.0.0\res
com.example.chamego_virtual.app-jetified-datastore-preferences-release-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c88585aeed2868437bd597f75b4b831\transformed\jetified-datastore-preferences-release\res
com.example.chamego_virtual.app-jetified-fragment-ktx-1.7.1-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5057f603aa2f34d8c005f0df6dc8ca94\transformed\jetified-fragment-ktx-1.7.1\res
com.example.chamego_virtual.app-jetified-window-java-1.2.0-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54693cfc10b86d613896531f8d4d8fdb\transformed\jetified-window-java-1.2.0\res
com.example.chamego_virtual.app-jetified-window-1.2.0-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65506c5965db684cd3fe458829c1a269\transformed\jetified-window-1.2.0\res
com.example.chamego_virtual.app-jetified-lifecycle-runtime-ktx-2.7.0-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6dd94d37a7122a3dc2558fe365c70839\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.chamego_virtual.app-fragment-1.7.1-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6eb87d2bef852723d21d08e38a3585c6\transformed\fragment-1.7.1\res
com.example.chamego_virtual.app-jetified-core-ktx-1.13.1-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7217b59d5e6cd848622a7bddb1c0d9c5\transformed\jetified-core-ktx-1.13.1\res
com.example.chamego_virtual.app-jetified-activity-ktx-1.8.1-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\748288c3aab132f5fac55f545babf3a1\transformed\jetified-activity-ktx-1.8.1\res
com.example.chamego_virtual.app-lifecycle-runtime-2.7.0-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\753c45fe82bc5b3b5499dd8371f7d8a7\transformed\lifecycle-runtime-2.7.0\res
com.example.chamego_virtual.app-jetified-firebase-common-21.0.0-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e6db8b7cb745974480119df1fcb8b44\transformed\jetified-firebase-common-21.0.0\res
com.example.chamego_virtual.app-jetified-datastore-release-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b0ddfad92bda52be246c36a9635cf2\transformed\jetified-datastore-release\res
com.example.chamego_virtual.app-jetified-savedstate-ktx-1.2.1-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\815092bfead357c492df1ca4173ee91c\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.chamego_virtual.app-jetified-core-1.0.0-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\838f9195529a0eb0dfeb9610d69f9803\transformed\jetified-core-1.0.0\res
com.example.chamego_virtual.app-coordinatorlayout-1.0.0-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8977a1c09af6145933cbd7953ad6fc2a\transformed\coordinatorlayout-1.0.0\res
com.example.chamego_virtual.app-slidingpanelayout-1.2.0-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b78e5ee99fc5b4ed0f2dd2b0614794e\transformed\slidingpanelayout-1.2.0\res
com.example.chamego_virtual.app-jetified-annotation-experimental-1.4.0-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c047370feeb4603efbf01cd4e723b50\transformed\jetified-annotation-experimental-1.4.0\res
com.example.chamego_virtual.app-jetified-play-services-basement-18.5.0-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c3eefca2d9a5f42152c30d1f581b28f\transformed\jetified-play-services-basement-18.5.0\res
com.example.chamego_virtual.app-jetified-firebase-messaging-24.1.1-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9433c1c991afc3f9b1bcf08d7dc19276\transformed\jetified-firebase-messaging-24.1.1\res
com.example.chamego_virtual.app-jetified-activity-1.8.1-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\980aacdd4a8fd9d7e92cb07fdea9b63f\transformed\jetified-activity-1.8.1\res
com.example.chamego_virtual.app-lifecycle-livedata-core-2.7.0-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99c2e8d0b2f613de235dbafd09af9484\transformed\lifecycle-livedata-core-2.7.0\res
com.example.chamego_virtual.app-jetified-ads-adservices-java-1.1.0-beta11-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d642e4b542668dd236a7f1d7cf2991e\transformed\jetified-ads-adservices-java-1.1.0-beta11\res
com.example.chamego_virtual.app-jetified-profileinstaller-1.3.1-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f6376e5e4555fe2849642d611bc9011\transformed\jetified-profileinstaller-1.3.1\res
com.example.chamego_virtual.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6f311c4a0f1dfd924de334c3dd888e8\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.chamego_virtual.app-core-runtime-2.2.0-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2001d4465d820f00ce02c55cab0f5d6\transformed\core-runtime-2.2.0\res
com.example.chamego_virtual.app-media-1.1.0-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba2c2b90c3641e52c83852e1592e6624\transformed\media-1.1.0\res
com.example.chamego_virtual.app-jetified-ads-adservices-1.1.0-beta11-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd38b806ba982e4d920f11c03249e664\transformed\jetified-ads-adservices-1.1.0-beta11\res
com.example.chamego_virtual.app-lifecycle-viewmodel-2.7.0-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ccee657f35e911bbd19cd8c5a0a51221\transformed\lifecycle-viewmodel-2.7.0\res
com.example.chamego_virtual.app-jetified-lifecycle-process-2.7.0-38 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3a01c48789496b004a78e0db47ba99\transformed\jetified-lifecycle-process-2.7.0\res
com.example.chamego_virtual.app-jetified-datastore-core-release-39 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5b81a73c972414463cea1ff186359fe\transformed\jetified-datastore-core-release\res
com.example.chamego_virtual.app-jetified-tracing-1.2.0-40 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1a4cf675f8dff0fcb2043e737b41a43\transformed\jetified-tracing-1.2.0\res
com.example.chamego_virtual.app-jetified-lifecycle-viewmodel-ktx-2.7.0-41 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e40f8ca068c7e6cde61d2e066421d418\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.chamego_virtual.app-jetified-appcompat-resources-1.1.0-42 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8889d19cc327cee1d7cfed501269976\transformed\jetified-appcompat-resources-1.1.0\res
com.example.chamego_virtual.app-debug-43 C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\android\app\src\debug\res
com.example.chamego_virtual.app-main-44 C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\android\app\src\main\res
com.example.chamego_virtual.app-pngs-45 C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\app\generated\res\pngs\debug
com.example.chamego_virtual.app-res-46 C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\app\generated\res\processDebugGoogleServices
com.example.chamego_virtual.app-resValues-47 C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\app\generated\res\resValues\debug
com.example.chamego_virtual.app-packageDebugResources-48 C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.chamego_virtual.app-packageDebugResources-49 C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.chamego_virtual.app-debug-50 C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\app\intermediates\merged_res\debug\mergeDebugResources
com.example.chamego_virtual.app-debug-51 C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_core\intermediates\packaged_res\debug\packageDebugResources
com.example.chamego_virtual.app-debug-52 C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\firebase_messaging\intermediates\packaged_res\debug\packageDebugResources
com.example.chamego_virtual.app-debug-53 C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\flutter_local_notifications\intermediates\packaged_res\debug\packageDebugResources
com.example.chamego_virtual.app-debug-54 C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\fluttertoast\intermediates\packaged_res\debug\packageDebugResources
com.example.chamego_virtual.app-debug-55 C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\path_provider_android\intermediates\packaged_res\debug\packageDebugResources
com.example.chamego_virtual.app-debug-56 C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\chamego_virtual\build\shared_preferences_android\intermediates\packaged_res\debug\packageDebugResources
