/// Configurações do aplicativo para diferentes ambientes
class AppConfig {
  // Configuração do ambiente atual
  static const Environment currentEnvironment = Environment.developmentPhysical;
  
  // URLs base para diferentes ambientes
  static const Map<Environment, String> _baseUrls = {
    Environment.development: 'http://********:5032/api', // Para emulador Android
    Environment.developmentPhysical: 'http://***************:5032/api', // Para dispositivos físicos
    Environment.developmentWeb: 'http://localhost:5032/api', // Para web/iOS Simulator
    Environment.production: 'http://*************/api', // Servidor de produção
  };
  
  // Obter URL base do ambiente atual
  static String get baseUrl => _baseUrls[currentEnvironment]!;
  
  // Verificar se está em desenvolvimento
  static bool get isDevelopment => currentEnvironment != Environment.production;
  
  // Configurações específicas por ambiente
  static bool get enableDebugLogs => isDevelopment;
  static bool get enableDetailedErrors => isDevelopment;
  
  // Configurações de timeout
  static const Duration apiTimeout = Duration(seconds: 30);
  static const Duration connectionTimeout = Duration(seconds: 10);
}

/// Enum para definir os ambientes disponíveis
enum Environment {
  development,        // Emulador Android (********)
  developmentPhysical, // Dispositivo físico na rede local
  developmentWeb,     // Web ou iOS Simulator (localhost)
  production,         // Servidor de produção
}

/// Extensão para obter informações sobre o ambiente
extension EnvironmentExtension on Environment {
  String get name {
    switch (this) {
      case Environment.development:
        return 'Desenvolvimento (Emulador)';
      case Environment.developmentPhysical:
        return 'Desenvolvimento (Dispositivo Físico)';
      case Environment.developmentWeb:
        return 'Desenvolvimento (Web/iOS)';
      case Environment.production:
        return 'Produção';
    }
  }
  
  String get description {
    switch (this) {
      case Environment.development:
        return 'Conecta ao servidor local via emulador Android (********:5032)';
      case Environment.developmentPhysical:
        return 'Conecta ao servidor local via rede WiFi (192.168.x.x:5032)';
      case Environment.developmentWeb:
        return 'Conecta ao servidor local via localhost (localhost:5032)';
      case Environment.production:
        return 'Conecta ao servidor de produção (*************)';
    }
  }
}
